// 微信API工具函数
const axios = require('axios');
const config = require('../config/config');

/**
 * 调用微信code2session接口
 * @param {String} code - 小程序登录凭证
 * @returns {Object} 微信返回的用户信息
 */
async function code2Session(code) {
  try {
    const url = config.wechat.apiUrl;
    const params = {
      appid: config.wechat.appId,
      secret: config.wechat.appSecret,
      js_code: code,
      grant_type: 'authorization_code'
    };

    console.log('调用微信code2session接口...');
    
    const response = await axios.get(url, { 
      params,
      timeout: 10000 // 10秒超时
    });

    const data = response.data;
    
    // 检查微信API返回的错误
    if (data.errcode && data.errcode !== 0) {
      const errorMessages = {
        40013: 'AppID无效',
        40014: 'AppSecret无效',
        40029: 'code无效',
        45011: 'API调用太频繁，请稍后再试',
        40226: '高风险等级用户，小程序登录拦截',
        -1: '系统繁忙，此时请开发者稍候再试'
      };
      
      const errorMessage = errorMessages[data.errcode] || `微信API错误: ${data.errmsg}`;
      throw new Error(errorMessage);
    }

    // 检查必要字段
    if (!data.openid) {
      throw new Error('微信API返回数据异常：缺少openid');
    }

    console.log('微信code2session调用成功，openid:', data.openid);
    
    return {
      openid: data.openid,
      session_key: data.session_key,
      unionid: data.unionid || null
    };

  } catch (error) {
    console.error('微信code2session调用失败:', error.message);
    
    if (error.code === 'ECONNABORTED') {
      throw new Error('微信API请求超时，请稍后重试');
    } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
      throw new Error('网络连接失败，请检查网络设置');
    } else if (error.response) {
      throw new Error(`微信API请求失败: ${error.response.status}`);
    } else {
      throw error;
    }
  }
}

/**
 * 验证微信小程序数据签名
 * @param {String} rawData - 原始数据
 * @param {String} signature - 签名
 * @param {String} sessionKey - 会话密钥
 * @returns {Boolean} 验证结果
 */
function verifySignature(rawData, signature, sessionKey) {
  const crypto = require('crypto');
  
  try {
    const hash = crypto
      .createHmac('sha1', sessionKey)
      .update(rawData)
      .digest('hex');
    
    return hash === signature;
  } catch (error) {
    console.error('验证微信数据签名失败:', error);
    return false;
  }
}

/**
 * 解密微信小程序加密数据
 * @param {String} encryptedData - 加密数据
 * @param {String} iv - 初始向量
 * @param {String} sessionKey - 会话密钥
 * @returns {Object} 解密后的数据
 */
function decryptData(encryptedData, iv, sessionKey) {
  const crypto = require('crypto');
  
  try {
    const sessionKeyBuffer = Buffer.from(sessionKey, 'base64');
    const encryptedDataBuffer = Buffer.from(encryptedData, 'base64');
    const ivBuffer = Buffer.from(iv, 'base64');
    
    const decipher = crypto.createDecipheriv('aes-128-cbc', sessionKeyBuffer, ivBuffer);
    decipher.setAutoPadding(true);
    
    let decrypted = decipher.update(encryptedDataBuffer, null, 'utf8');
    decrypted += decipher.final('utf8');
    
    const decryptedData = JSON.parse(decrypted);
    
    // 验证水印
    if (decryptedData.watermark.appid !== config.wechat.appId) {
      throw new Error('数据水印验证失败');
    }
    
    return decryptedData;
  } catch (error) {
    console.error('解密微信数据失败:', error);
    throw new Error('数据解密失败');
  }
}

/**
 * 获取微信小程序访问令牌
 * @returns {String} access_token
 */
async function getAccessToken() {
  try {
    const url = 'https://api.weixin.qq.com/cgi-bin/token';
    const params = {
      grant_type: 'client_credential',
      appid: config.wechat.appId,
      secret: config.wechat.appSecret
    };

    const response = await axios.get(url, { 
      params,
      timeout: 10000
    });

    const data = response.data;
    
    if (data.errcode && data.errcode !== 0) {
      throw new Error(`获取access_token失败: ${data.errmsg}`);
    }

    return data.access_token;
  } catch (error) {
    console.error('获取微信access_token失败:', error.message);
    throw error;
  }
}

/**
 * 发送小程序订阅消息
 * @param {String} openid - 用户openid
 * @param {String} templateId - 模板ID
 * @param {Object} data - 消息数据
 * @param {String} page - 跳转页面
 * @returns {Boolean} 发送结果
 */
async function sendSubscribeMessage(openid, templateId, data, page = '') {
  try {
    const accessToken = await getAccessToken();
    const url = `https://api.weixin.qq.com/cgi-bin/message/subscribe/send?access_token=${accessToken}`;
    
    const postData = {
      touser: openid,
      template_id: templateId,
      page: page,
      data: data
    };

    const response = await axios.post(url, postData, {
      timeout: 10000
    });

    const result = response.data;
    
    if (result.errcode === 0) {
      console.log('订阅消息发送成功');
      return true;
    } else {
      console.error('订阅消息发送失败:', result.errmsg);
      return false;
    }
  } catch (error) {
    console.error('发送订阅消息异常:', error.message);
    return false;
  }
}

module.exports = {
  code2Session,
  verifySignature,
  decryptData,
  getAccessToken,
  sendSubscribeMessage
};
