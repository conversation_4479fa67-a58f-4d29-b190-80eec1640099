// 数据库连接配置
const mysql = require('mysql2/promise');
const config = require('./config');

// 创建数据库连接池
const pool = mysql.createPool({
  host: config.database.host,
  port: config.database.port,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  charset: config.database.charset,
  timezone: config.database.timezone,
  connectionLimit: config.database.connectionLimit,
  acquireTimeout: config.database.acquireTimeout,
  timeout: config.database.timeout,
  // 自动重连
  reconnect: true,
  // 连接空闲超时
  idleTimeout: 300000,
  // 启用多语句查询
  multipleStatements: false
});

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('✅ 数据库连接成功');
    
    // 测试查询
    const [rows] = await connection.execute('SELECT 1 as test');
    console.log('✅ 数据库查询测试成功:', rows[0]);
    
    connection.release();
    return true;
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    return false;
  }
}

// 执行查询的通用方法
async function query(sql, params = []) {
  try {
    const [rows, fields] = await pool.execute(sql, params);
    return { success: true, data: rows, fields };
  } catch (error) {
    console.error('数据库查询错误:', error);
    return { success: false, error: error.message };
  }
}

// 执行事务的方法
async function transaction(callback) {
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const result = await callback(connection);
    
    await connection.commit();
    connection.release();
    
    return { success: true, data: result };
  } catch (error) {
    await connection.rollback();
    connection.release();
    
    console.error('事务执行错误:', error);
    return { success: false, error: error.message };
  }
}

// 关闭数据库连接池
async function closePool() {
  try {
    await pool.end();
    console.log('✅ 数据库连接池已关闭');
  } catch (error) {
    console.error('❌ 关闭数据库连接池失败:', error);
  }
}

// 优雅关闭处理
process.on('SIGINT', async () => {
  console.log('\n正在关闭数据库连接...');
  await closePool();
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n正在关闭数据库连接...');
  await closePool();
  process.exit(0);
});

module.exports = {
  pool,
  query,
  transaction,
  testConnection,
  closePool
};
