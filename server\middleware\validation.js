// 请求验证中间件
const Joi = require('joi');

/**
 * 创建验证中间件
 * @param {Object} schema - Joi验证规则
 * @param {String} source - 验证数据源 ('body', 'query', 'params')
 * @returns {Function} 中间件函数
 */
function validate(schema, source = 'body') {
  return (req, res, next) => {
    const data = req[source];
    const { error, value } = schema.validate(data, {
      abortEarly: false, // 返回所有错误
      allowUnknown: false, // 不允许未知字段
      stripUnknown: true // 移除未知字段
    });

    if (error) {
      const errors = error.details.map(detail => ({
        field: detail.path.join('.'),
        message: detail.message,
        value: detail.context.value
      }));

      return res.status(400).json({
        code: 400,
        message: '请求参数验证失败',
        data: {
          errors: errors
        }
      });
    }

    // 将验证后的数据替换原始数据
    req[source] = value;
    next();
  };
}

// 常用验证规则
const commonSchemas = {
  // 分页参数
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(10),
    sort: Joi.string().valid('asc', 'desc').default('desc'),
    sortBy: Joi.string().default('created_at')
  }),

  // ID参数
  id: Joi.object({
    id: Joi.number().integer().positive().required()
  }),

  // 用户信息更新
  userUpdate: Joi.object({
    nickname: Joi.string().min(1).max(50).trim(),
    avatar_url: Joi.string().uri().max(500),
    gender: Joi.number().integer().valid(0, 1, 2),
    country: Joi.string().max(50).trim(),
    province: Joi.string().max(50).trim(),
    city: Joi.string().max(50).trim(),
    language: Joi.string().max(20).trim(),
    phone: Joi.string().pattern(/^1[3-9]\d{9}$/).message('手机号格式不正确'),
    email: Joi.string().email().max(100).trim()
  }).min(1), // 至少需要一个字段

  // 登录请求
  login: Joi.object({
    code: Joi.string().required().min(1).max(100).trim().messages({
      'string.empty': 'code不能为空',
      'string.min': 'code长度不能少于1位',
      'string.max': 'code长度不能超过100位',
      'any.required': 'code是必填字段'
    })
  }),

  // 刷新Token请求
  refreshToken: Joi.object({
    refreshToken: Joi.string().required().trim().messages({
      'string.empty': '刷新令牌不能为空',
      'any.required': '刷新令牌是必填字段'
    })
  })
};

/**
 * 验证请求体
 * @param {Object} schema - Joi验证规则
 * @returns {Function} 中间件函数
 */
function validateBody(schema) {
  return validate(schema, 'body');
}

/**
 * 验证查询参数
 * @param {Object} schema - Joi验证规则
 * @returns {Function} 中间件函数
 */
function validateQuery(schema) {
  return validate(schema, 'query');
}

/**
 * 验证路径参数
 * @param {Object} schema - Joi验证规则
 * @returns {Function} 中间件函数
 */
function validateParams(schema) {
  return validate(schema, 'params');
}

/**
 * 验证文件上传
 * @param {Object} options - 验证选项
 * @returns {Function} 中间件函数
 */
function validateFile(options = {}) {
  const {
    required = false,
    maxSize = 5 * 1024 * 1024, // 5MB
    allowedTypes = ['image/jpeg', 'image/png', 'image/gif'],
    fieldName = 'file'
  } = options;

  return (req, res, next) => {
    const file = req.files && req.files[fieldName];

    if (required && !file) {
      return res.status(400).json({
        code: 400,
        message: '缺少必需的文件',
        data: null
      });
    }

    if (file) {
      // 检查文件大小
      if (file.size > maxSize) {
        return res.status(400).json({
          code: 400,
          message: `文件大小不能超过 ${Math.round(maxSize / 1024 / 1024)}MB`,
          data: null
        });
      }

      // 检查文件类型
      if (!allowedTypes.includes(file.mimetype)) {
        return res.status(400).json({
          code: 400,
          message: `不支持的文件类型，仅支持: ${allowedTypes.join(', ')}`,
          data: null
        });
      }
    }

    next();
  };
}

/**
 * 自定义验证函数
 * @param {Function} validator - 验证函数
 * @param {String} message - 错误消息
 * @returns {Function} 中间件函数
 */
function customValidate(validator, message = '验证失败') {
  return (req, res, next) => {
    try {
      const isValid = validator(req);
      if (isValid === true) {
        next();
      } else {
        const errorMessage = typeof isValid === 'string' ? isValid : message;
        res.status(400).json({
          code: 400,
          message: errorMessage,
          data: null
        });
      }
    } catch (error) {
      console.error('自定义验证错误:', error);
      res.status(500).json({
        code: 500,
        message: '验证过程中发生错误',
        data: null
      });
    }
  };
}

module.exports = {
  validate,
  validateBody,
  validateQuery,
  validateParams,
  validateFile,
  customValidate,
  schemas: commonSchemas
};
