// 身份验证控制器
const User = require('../models/User');
const { code2Session } = require('../utils/wechat');
const { generateToken, generateRefreshToken, verifyToken } = require('../utils/jwt');

/**
 * 微信小程序登录
 */
async function wechatLogin(req, res) {
  try {
    const { code } = req.body;
    
    // 验证参数
    if (!code) {
      return res.status(400).json({
        code: 400,
        message: '缺少登录凭证code',
        data: null
      });
    }

    // 获取客户端IP
    const clientIp = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];

    console.log('开始处理微信登录，code:', code);

    // 调用微信API获取用户信息
    const wechatUserInfo = await code2Session(code);
    
    console.log('微信API调用成功，openid:', wechatUserInfo.openid);

    // 查找或创建用户
    let user = await User.findByOpenId(wechatUserInfo.openid);
    
    if (user) {
      // 用户已存在，更新最后登录信息
      console.log('用户已存在，ID:', user.id);
      await user.updateLastLogin(clientIp);
    } else {
      // 创建新用户
      console.log('创建新用户...');
      user = await User.create({
        openid: wechatUserInfo.openid,
        unionid: wechatUserInfo.unionid,
        ip: clientIp
      });
      console.log('新用户创建成功，ID:', user.id);
    }

    // 生成JWT Token
    const tokenPayload = {
      userId: user.id,
      openid: user.openid,
      loginTime: Date.now()
    };

    const accessToken = generateToken(tokenPayload);
    const refreshToken = generateRefreshToken({ userId: user.id });

    // 返回登录成功响应
    res.json({
      code: 200,
      message: '登录成功',
      data: {
        token: accessToken,
        refreshToken: refreshToken,
        user: user.toSafeObject(),
        isNewUser: !user.last_login_time || user.created_at === user.updated_at
      }
    });

    console.log('用户登录成功，用户ID:', user.id);

  } catch (error) {
    console.error('微信登录失败:', error.message);
    
    let message = '登录失败，请稍后重试';
    let code = 500;
    
    // 根据错误类型返回不同的错误信息
    if (error.message.includes('AppID') || error.message.includes('AppSecret')) {
      message = '服务器配置错误';
      code = 500;
    } else if (error.message.includes('code无效')) {
      message = '登录凭证无效，请重新登录';
      code = 400;
    } else if (error.message.includes('API调用太频繁')) {
      message = '登录请求过于频繁，请稍后再试';
      code = 429;
    } else if (error.message.includes('网络')) {
      message = '网络连接失败，请检查网络后重试';
      code = 503;
    }

    res.status(code).json({
      code: code,
      message: message,
      data: null
    });
  }
}

/**
 * 刷新Token
 */
async function refreshToken(req, res) {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({
        code: 400,
        message: '缺少刷新令牌',
        data: null
      });
    }

    // 验证刷新Token
    const decoded = verifyToken(refreshToken);
    
    // 查找用户
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        code: 401,
        message: '用户不存在',
        data: null
      });
    }

    if (user.status !== 1) {
      return res.status(401).json({
        code: 401,
        message: '用户账号已被禁用',
        data: null
      });
    }

    // 生成新的访问Token
    const tokenPayload = {
      userId: user.id,
      openid: user.openid,
      loginTime: Date.now()
    };

    const newAccessToken = generateToken(tokenPayload);

    res.json({
      code: 200,
      message: 'Token刷新成功',
      data: {
        token: newAccessToken,
        user: user.toSafeObject()
      }
    });

  } catch (error) {
    console.error('Token刷新失败:', error.message);
    
    let message = 'Token刷新失败';
    let code = 401;
    
    if (error.message === 'Token已过期') {
      message = '刷新令牌已过期，请重新登录';
    } else if (error.message === 'Token无效') {
      message = '刷新令牌无效，请重新登录';
    }

    res.status(code).json({
      code: code,
      message: message,
      data: null
    });
  }
}

/**
 * 获取当前用户信息
 */
async function getCurrentUser(req, res) {
  try {
    // 用户信息已经在认证中间件中设置
    const user = req.user;
    
    res.json({
      code: 200,
      message: '获取用户信息成功',
      data: {
        user: user.toSafeObject()
      }
    });

  } catch (error) {
    console.error('获取用户信息失败:', error.message);
    
    res.status(500).json({
      code: 500,
      message: '获取用户信息失败',
      data: null
    });
  }
}

/**
 * 用户登出
 */
async function logout(req, res) {
  try {
    // 这里可以添加登出逻辑，比如将Token加入黑名单
    // 目前只是返回成功响应，实际的Token失效由客户端处理
    
    res.json({
      code: 200,
      message: '登出成功',
      data: null
    });

  } catch (error) {
    console.error('用户登出失败:', error.message);
    
    res.status(500).json({
      code: 500,
      message: '登出失败',
      data: null
    });
  }
}

/**
 * 检查Token有效性
 */
async function checkToken(req, res) {
  try {
    // Token验证已经在认证中间件中完成
    const user = req.user;
    const tokenPayload = req.tokenPayload;
    
    res.json({
      code: 200,
      message: 'Token有效',
      data: {
        valid: true,
        user: user.toSafeObject(),
        expiresAt: new Date(tokenPayload.exp * 1000).toISOString()
      }
    });

  } catch (error) {
    console.error('Token检查失败:', error.message);
    
    res.status(500).json({
      code: 500,
      message: 'Token检查失败',
      data: null
    });
  }
}

module.exports = {
  wechatLogin,
  refreshToken,
  getCurrentUser,
  logout,
  checkToken
};
