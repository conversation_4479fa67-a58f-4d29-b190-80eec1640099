// 小程序入口文件
const api = require('./utils/api');
const auth = require('./utils/auth');

App({
  // 全局数据
  globalData: {
    userInfo: null,
    token: null,
    isLogin: false,
    serverUrl: 'http://localhost:3000' // 后端服务器地址，请根据实际情况修改
  },

  // 小程序初始化
  onLaunch: function (options) {
    console.log('小程序启动', options);
    
    // 检查小程序版本更新
    this.checkForUpdate();
    
    // 初始化登录状态
    this.initLoginStatus();
  },

  // 小程序显示
  onShow: function (options) {
    console.log('小程序显示', options);
  },

  // 小程序隐藏
  onHide: function () {
    console.log('小程序隐藏');
  },

  // 小程序错误
  onError: function (msg) {
    console.error('小程序错误:', msg);
  },

  // 页面不存在
  onPageNotFound: function (res) {
    console.log('页面不存在:', res);
    wx.redirectTo({
      url: '/pages/index/index'
    });
  },

  // 检查小程序更新
  checkForUpdate: function () {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager();
      
      updateManager.onCheckForUpdate(function (res) {
        console.log('检查更新结果:', res.hasUpdate);
      });

      updateManager.onUpdateReady(function () {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: function (res) {
            if (res.confirm) {
              updateManager.applyUpdate();
            }
          }
        });
      });

      updateManager.onUpdateFailed(function () {
        wx.showModal({
          title: '更新失败',
          content: '新版本下载失败，请检查网络后重试',
          showCancel: false
        });
      });
    }
  },

  // 初始化登录状态
  initLoginStatus: function () {
    try {
      // 从本地存储获取token
      const token = wx.getStorageSync('token');
      const userInfo = wx.getStorageSync('userInfo');
      
      if (token && userInfo) {
        this.globalData.token = token;
        this.globalData.userInfo = userInfo;
        this.globalData.isLogin = true;
        
        console.log('从本地存储恢复登录状态');
        
        // 验证token有效性
        this.checkTokenValid();
      } else {
        console.log('未找到本地登录信息');
        this.clearLoginStatus();
      }
    } catch (error) {
      console.error('初始化登录状态失败:', error);
      this.clearLoginStatus();
    }
  },

  // 检查token有效性
  checkTokenValid: function () {
    auth.checkToken()
      .then(result => {
        if (result.code === 200) {
          console.log('Token验证成功');
          // 更新用户信息
          this.globalData.userInfo = result.data.user;
          wx.setStorageSync('userInfo', result.data.user);
        } else {
          console.log('Token验证失败:', result.message);
          this.clearLoginStatus();
        }
      })
      .catch(error => {
        console.error('Token验证异常:', error);
        this.clearLoginStatus();
      });
  },

  // 设置登录状态
  setLoginStatus: function (token, userInfo) {
    try {
      this.globalData.token = token;
      this.globalData.userInfo = userInfo;
      this.globalData.isLogin = true;
      
      // 保存到本地存储
      wx.setStorageSync('token', token);
      wx.setStorageSync('userInfo', userInfo);
      
      console.log('登录状态设置成功');
    } catch (error) {
      console.error('设置登录状态失败:', error);
    }
  },

  // 清除登录状态
  clearLoginStatus: function () {
    try {
      this.globalData.token = null;
      this.globalData.userInfo = null;
      this.globalData.isLogin = false;
      
      // 清除本地存储
      wx.removeStorageSync('token');
      wx.removeStorageSync('userInfo');
      
      console.log('登录状态已清除');
    } catch (error) {
      console.error('清除登录状态失败:', error);
    }
  },

  // 获取用户信息
  getUserInfo: function () {
    return this.globalData.userInfo;
  },

  // 获取token
  getToken: function () {
    return this.globalData.token;
  },

  // 检查是否已登录
  isLoggedIn: function () {
    return this.globalData.isLogin && this.globalData.token;
  },

  // 显示加载提示
  showLoading: function (title = '加载中...') {
    wx.showLoading({
      title: title,
      mask: true
    });
  },

  // 隐藏加载提示
  hideLoading: function () {
    wx.hideLoading();
  },

  // 显示成功提示
  showSuccess: function (title = '操作成功') {
    wx.showToast({
      title: title,
      icon: 'success',
      duration: 2000
    });
  },

  // 显示错误提示
  showError: function (title = '操作失败') {
    wx.showToast({
      title: title,
      icon: 'error',
      duration: 2000
    });
  },

  // 显示普通提示
  showToast: function (title, icon = 'none') {
    wx.showToast({
      title: title,
      icon: icon,
      duration: 2000
    });
  }
});
