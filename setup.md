# 微信小程序登录系统 - 完整设置指南

## 🚀 快速开始

### 1. 环境要求

- **Node.js**: 14.0 或更高版本
- **MySQL**: 5.7 或更高版本
- **微信开发者工具**: 最新版本
- **微信小程序账号**: 已注册并获得AppID

### 2. 获取微信小程序配置

1. 登录 [微信公众平台](https://mp.weixin.qq.com/)
2. 进入"开发" → "开发管理" → "开发设置"
3. 记录下你的 **AppID** 和 **AppSecret**

### 3. 数据库设置

#### 3.1 安装MySQL
```bash
# Windows: 下载MySQL安装包
# macOS: brew install mysql
# Ubuntu: sudo apt-get install mysql-server
```

#### 3.2 启动MySQL服务
```bash
# Windows: 在服务中启动MySQL
# macOS/Linux: sudo systemctl start mysql
```

#### 3.3 创建数据库用户（可选）
```sql
-- 如果使用root用户，可以跳过此步骤
CREATE USER 'wechat_user'@'localhost' IDENTIFIED BY '123456';
GRANT ALL PRIVILEGES ON wechat_miniprogram.* TO 'wechat_user'@'localhost';
FLUSH PRIVILEGES;
```

### 4. 后端服务器设置

#### 4.1 安装依赖
```bash
cd server
npm install
```

#### 4.2 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入你的配置
```

**重要配置项：**
```env
# 微信小程序配置
WECHAT_APP_ID=你的小程序AppID
WECHAT_APP_SECRET=你的小程序AppSecret

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=wechat_miniprogram

# JWT密钥（生产环境请使用复杂密钥）
JWT_SECRET=your-super-secret-jwt-key
```

#### 4.3 初始化数据库
```bash
# 运行数据库初始化脚本
node scripts/init-database.js
```

#### 4.4 启动服务器
```bash
# 开发模式
npm run dev

# 生产模式
npm start
```

服务器将在 `http://localhost:3000` 启动

### 5. 小程序前端设置

#### 5.1 配置服务器地址
编辑 `miniprogram/app.js`，修改服务器地址：
```javascript
globalData: {
  serverUrl: 'http://localhost:3000' // 改为你的服务器地址
}
```

#### 5.2 配置小程序AppID
在微信开发者工具中：
1. 导入项目，选择 `miniprogram` 目录
2. 填入你的小程序AppID
3. 项目名称可以自定义

#### 5.3 配置服务器域名（生产环境）
在微信公众平台中：
1. 进入"开发" → "开发管理" → "开发设置"
2. 在"服务器域名"中添加你的后端服务器域名
3. request合法域名：`https://你的域名`

### 6. 测试系统

#### 6.1 测试后端API
```bash
# 测试健康检查接口
curl http://localhost:3000/health

# 查看API文档
curl http://localhost:3000/api/docs
```

#### 6.2 测试小程序
1. 在微信开发者工具中点击"编译"
2. 点击"登录"按钮测试登录流程
3. 检查控制台是否有错误信息

## 🔧 常见问题解决

### 问题1: 数据库连接失败
**错误**: `ER_ACCESS_DENIED_ERROR`
**解决**: 检查数据库用户名、密码和权限

### 问题2: 微信登录失败
**错误**: `AppID无效` 或 `AppSecret无效`
**解决**: 
1. 确认AppID和AppSecret正确
2. 检查小程序是否已发布或在开发者工具中测试

### 问题3: 网络请求失败
**错误**: `request:fail`
**解决**:
1. 检查服务器是否启动
2. 确认服务器地址配置正确
3. 生产环境需要配置合法域名

### 问题4: Token验证失败
**错误**: `Token无效`
**解决**:
1. 检查JWT_SECRET配置
2. 确认时间同步
3. 检查Token是否过期

## 📝 部署到生产环境

### 1. 服务器部署
```bash
# 使用PM2管理进程
npm install -g pm2
pm2 start app.js --name wechat-server

# 或使用Docker
docker build -t wechat-server .
docker run -d -p 3000:3000 wechat-server
```

### 2. 域名和HTTPS
- 购买域名并配置DNS
- 申请SSL证书
- 配置Nginx反向代理

### 3. 数据库优化
- 配置数据库连接池
- 设置数据库备份
- 优化查询性能

## 🛡️ 安全建议

1. **生产环境配置**:
   - 使用强密码和复杂的JWT密钥
   - 启用HTTPS
   - 配置防火墙

2. **数据库安全**:
   - 不要使用root用户
   - 定期备份数据
   - 限制数据库访问权限

3. **API安全**:
   - 启用请求频率限制
   - 验证所有输入参数
   - 记录安全事件日志

## 📞 技术支持

如果遇到问题，请：
1. 查看服务器日志：`tail -f logs/*.log`
2. 检查微信开发者工具控制台
3. 参考微信官方文档
4. 提交Issue到项目仓库

---

**祝你使用愉快！** 🎉
