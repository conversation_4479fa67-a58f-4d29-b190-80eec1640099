// API请求工具
const app = getApp();

// 请求基础配置
const config = {
  baseUrl: '', // 将在request方法中动态获取
  timeout: 10000,
  header: {
    'Content-Type': 'application/json'
  }
};

/**
 * 发送HTTP请求
 * @param {Object} options 请求配置
 * @returns {Promise} 请求结果
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 获取全局配置的服务器地址
    const baseUrl = app.globalData.serverUrl;
    
    // 构建完整URL
    const url = options.url.startsWith('http') ? options.url : baseUrl + options.url;
    
    // 构建请求头
    const header = {
      ...config.header,
      ...options.header
    };
    
    // 添加认证token
    const token = app.getToken();
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }

    console.log('发送请求:', {
      method: options.method || 'GET',
      url: url,
      data: options.data,
      header: header
    });

    wx.request({
      url: url,
      method: options.method || 'GET',
      data: options.data || {},
      header: header,
      timeout: options.timeout || config.timeout,
      success: (res) => {
        console.log('请求成功:', res);
        
        // 检查HTTP状态码
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 检查业务状态码
          if (res.data && res.data.code !== undefined) {
            if (res.data.code === 200) {
              resolve(res.data);
            } else if (res.data.code === 401) {
              // Token过期或无效，清除登录状态
              console.log('Token无效，清除登录状态');
              app.clearLoginStatus();
              
              // 跳转到登录页面
              wx.reLaunch({
                url: '/pages/login/login'
              });
              
              reject(new Error(res.data.message || '身份验证失败'));
            } else {
              reject(new Error(res.data.message || '请求失败'));
            }
          } else {
            resolve(res.data);
          }
        } else {
          reject(new Error(`HTTP错误: ${res.statusCode}`));
        }
      },
      fail: (err) => {
        console.error('请求失败:', err);
        
        let message = '网络请求失败';
        if (err.errMsg) {
          if (err.errMsg.includes('timeout')) {
            message = '请求超时，请检查网络';
          } else if (err.errMsg.includes('fail')) {
            message = '网络连接失败';
          }
        }
        
        reject(new Error(message));
      }
    });
  });
}

/**
 * GET请求
 * @param {String} url 请求地址
 * @param {Object} data 请求参数
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function get(url, data = {}, options = {}) {
  // 将参数拼接到URL中
  if (Object.keys(data).length > 0) {
    const params = Object.keys(data)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
      .join('&');
    url += (url.includes('?') ? '&' : '?') + params;
  }
  
  return request({
    url: url,
    method: 'GET',
    ...options
  });
}

/**
 * POST请求
 * @param {String} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function post(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'POST',
    data: data,
    ...options
  });
}

/**
 * PUT请求
 * @param {String} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function put(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'PUT',
    data: data,
    ...options
  });
}

/**
 * DELETE请求
 * @param {String} url 请求地址
 * @param {Object} data 请求数据
 * @param {Object} options 其他配置
 * @returns {Promise} 请求结果
 */
function del(url, data = {}, options = {}) {
  return request({
    url: url,
    method: 'DELETE',
    data: data,
    ...options
  });
}

/**
 * 上传文件
 * @param {String} url 上传地址
 * @param {String} filePath 文件路径
 * @param {String} name 文件字段名
 * @param {Object} formData 其他表单数据
 * @returns {Promise} 上传结果
 */
function uploadFile(url, filePath, name = 'file', formData = {}) {
  return new Promise((resolve, reject) => {
    const baseUrl = app.globalData.serverUrl;
    const fullUrl = url.startsWith('http') ? url : baseUrl + url;
    
    // 构建请求头
    const header = {};
    const token = app.getToken();
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }

    console.log('上传文件:', {
      url: fullUrl,
      filePath: filePath,
      name: name,
      formData: formData
    });

    wx.uploadFile({
      url: fullUrl,
      filePath: filePath,
      name: name,
      formData: formData,
      header: header,
      success: (res) => {
        console.log('文件上传成功:', res);
        
        try {
          const data = JSON.parse(res.data);
          if (data.code === 200) {
            resolve(data);
          } else {
            reject(new Error(data.message || '上传失败'));
          }
        } catch (error) {
          reject(new Error('响应数据解析失败'));
        }
      },
      fail: (err) => {
        console.error('文件上传失败:', err);
        reject(new Error('文件上传失败'));
      }
    });
  });
}

/**
 * 下载文件
 * @param {String} url 下载地址
 * @returns {Promise} 下载结果
 */
function downloadFile(url) {
  return new Promise((resolve, reject) => {
    const baseUrl = app.globalData.serverUrl;
    const fullUrl = url.startsWith('http') ? url : baseUrl + url;
    
    // 构建请求头
    const header = {};
    const token = app.getToken();
    if (token) {
      header.Authorization = `Bearer ${token}`;
    }

    console.log('下载文件:', fullUrl);

    wx.downloadFile({
      url: fullUrl,
      header: header,
      success: (res) => {
        console.log('文件下载成功:', res);
        
        if (res.statusCode === 200) {
          resolve(res);
        } else {
          reject(new Error('下载失败'));
        }
      },
      fail: (err) => {
        console.error('文件下载失败:', err);
        reject(new Error('文件下载失败'));
      }
    });
  });
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  uploadFile,
  downloadFile
};
