// 日志工具
const fs = require('fs');
const path = require('path');
const config = require('../config/config');

// 日志级别
const LOG_LEVELS = {
  ERROR: 0,
  WARN: 1,
  INFO: 2,
  DEBUG: 3
};

// 当前日志级别
const currentLevel = LOG_LEVELS[config.logging.level.toUpperCase()] || LOG_LEVELS.INFO;

// 确保日志目录存在
function ensureLogDir() {
  if (config.logging.enableFile) {
    const logDir = config.logging.logDir;
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }
}

/**
 * 格式化日志消息
 * @param {String} level - 日志级别
 * @param {String} message - 日志消息
 * @param {Object} meta - 元数据
 * @returns {String} 格式化后的日志
 */
function formatLog(level, message, meta = {}) {
  const timestamp = new Date().toISOString();
  const metaStr = Object.keys(meta).length > 0 ? ` ${JSON.stringify(meta)}` : '';
  return `[${timestamp}] [${level}] ${message}${metaStr}`;
}

/**
 * 写入日志文件
 * @param {String} level - 日志级别
 * @param {String} formattedLog - 格式化后的日志
 */
function writeToFile(level, formattedLog) {
  if (!config.logging.enableFile) {
    return;
  }

  try {
    const logDir = config.logging.logDir;
    const date = new Date().toISOString().split('T')[0];
    const filename = `${date}.log`;
    const filepath = path.join(logDir, filename);
    
    fs.appendFileSync(filepath, formattedLog + '\n');
  } catch (error) {
    console.error('写入日志文件失败:', error);
  }
}

/**
 * 输出到控制台
 * @param {String} level - 日志级别
 * @param {String} formattedLog - 格式化后的日志
 */
function writeToConsole(level, formattedLog) {
  if (!config.logging.enableConsole) {
    return;
  }

  switch (level) {
    case 'ERROR':
      console.error(formattedLog);
      break;
    case 'WARN':
      console.warn(formattedLog);
      break;
    case 'INFO':
      console.info(formattedLog);
      break;
    case 'DEBUG':
      console.debug(formattedLog);
      break;
    default:
      console.log(formattedLog);
  }
}

/**
 * 通用日志方法
 * @param {String} level - 日志级别
 * @param {String} message - 日志消息
 * @param {Object} meta - 元数据
 */
function log(level, message, meta = {}) {
  const levelValue = LOG_LEVELS[level];
  
  if (levelValue > currentLevel) {
    return; // 跳过低级别日志
  }

  const formattedLog = formatLog(level, message, meta);
  
  writeToConsole(level, formattedLog);
  writeToFile(level, formattedLog);
}

/**
 * 错误日志
 * @param {String} message - 日志消息
 * @param {Object} meta - 元数据
 */
function error(message, meta = {}) {
  log('ERROR', message, meta);
}

/**
 * 警告日志
 * @param {String} message - 日志消息
 * @param {Object} meta - 元数据
 */
function warn(message, meta = {}) {
  log('WARN', message, meta);
}

/**
 * 信息日志
 * @param {String} message - 日志消息
 * @param {Object} meta - 元数据
 */
function info(message, meta = {}) {
  log('INFO', message, meta);
}

/**
 * 调试日志
 * @param {String} message - 日志消息
 * @param {Object} meta - 元数据
 */
function debug(message, meta = {}) {
  log('DEBUG', message, meta);
}

/**
 * 请求日志中间件
 */
function requestLogger(req, res, next) {
  const start = Date.now();
  const requestId = req.requestId || 'unknown';
  
  // 记录请求开始
  info('请求开始', {
    requestId,
    method: req.method,
    url: req.originalUrl,
    ip: req.ip,
    userAgent: req.headers['user-agent']
  });

  // 监听响应结束
  const originalSend = res.send;
  res.send = function(data) {
    const duration = Date.now() - start;
    
    // 记录请求结束
    info('请求结束', {
      requestId,
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('content-length') || 0
    });
    
    originalSend.call(this, data);
  };

  next();
}

/**
 * 错误日志中间件
 */
function errorLogger(err, req, res, next) {
  const requestId = req.requestId || 'unknown';
  
  error('请求错误', {
    requestId,
    method: req.method,
    url: req.originalUrl,
    error: err.message,
    stack: err.stack,
    ip: req.ip
  });

  next(err);
}

/**
 * 数据库操作日志
 * @param {String} operation - 操作类型
 * @param {String} table - 表名
 * @param {Object} data - 操作数据
 * @param {Object} result - 操作结果
 */
function dbLog(operation, table, data = {}, result = {}) {
  debug('数据库操作', {
    operation,
    table,
    data: JSON.stringify(data),
    success: result.success || false,
    affectedRows: result.affectedRows || 0,
    insertId: result.insertId || null
  });
}

/**
 * 安全事件日志
 * @param {String} event - 事件类型
 * @param {Object} details - 事件详情
 * @param {Object} req - 请求对象
 */
function securityLog(event, details = {}, req = {}) {
  warn('安全事件', {
    event,
    details,
    ip: req.ip,
    userAgent: req.headers && req.headers['user-agent'],
    timestamp: new Date().toISOString()
  });
}

/**
 * 性能日志
 * @param {String} operation - 操作名称
 * @param {Number} duration - 耗时（毫秒）
 * @param {Object} meta - 额外信息
 */
function performanceLog(operation, duration, meta = {}) {
  const level = duration > 1000 ? 'WARN' : 'INFO';
  log(level, `性能监控: ${operation}`, {
    duration: `${duration}ms`,
    ...meta
  });
}

/**
 * 清理旧日志文件
 * @param {Number} days - 保留天数
 */
function cleanOldLogs(days = 30) {
  if (!config.logging.enableFile) {
    return;
  }

  try {
    const logDir = config.logging.logDir;
    const files = fs.readdirSync(logDir);
    const cutoffTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    files.forEach(file => {
      const filepath = path.join(logDir, file);
      const stats = fs.statSync(filepath);
      
      if (stats.mtime.getTime() < cutoffTime) {
        fs.unlinkSync(filepath);
        info(`删除旧日志文件: ${file}`);
      }
    });
  } catch (error) {
    error('清理旧日志文件失败', { error: error.message });
  }
}

// 初始化
ensureLogDir();

// 定期清理旧日志（每天执行一次）
if (config.logging.enableFile) {
  setInterval(() => {
    cleanOldLogs();
  }, 24 * 60 * 60 * 1000);
}

module.exports = {
  error,
  warn,
  info,
  debug,
  requestLogger,
  errorLogger,
  dbLog,
  securityLog,
  performanceLog,
  cleanOldLogs
};
