# 图片资源说明

这个目录包含小程序所需的图片资源。由于这是一个演示项目，以下列出了需要的图片文件：

## 必需的图片文件

### 应用图标
- `logo.png` - 应用Logo (160x160px)
- `default-avatar.png` - 默认头像 (120x120px)

### 登录页面
- `wechat.png` - 微信图标 (40x40px)
- `secure.png` - 安全图标 (60x60px)
- `fast.png` - 快速图标 (60x60px)
- `privacy.png` - 隐私图标 (60x60px)

### 首页
- `login-prompt.png` - 登录提示图标 (120x120px)
- `profile-menu.png` - 个人中心菜单图标 (60x60px)
- `info-menu.png` - 信息菜单图标 (60x60px)
- `token-menu.png` - Token菜单图标 (60x60px)
- `refresh-menu.png` - 刷新菜单图标 (60x60px)

### 个人中心
- `camera.png` - 相机图标 (30x30px)
- `arrow-right.png` - 右箭头图标 (24x24px)
- `token.png` - Token图标 (40x40px)
- `refresh.png` - 刷新图标 (40x40px)
- `export.png` - 导出图标 (40x40px)
- `close.png` - 关闭图标 (40x40px)

### TabBar图标
- `home.png` - 首页图标 (未选中)
- `home-active.png` - 首页图标 (选中)
- `profile.png` - 个人中心图标 (未选中)
- `profile-active.png` - 个人中心图标 (选中)

## 图片规格建议

- 格式：PNG（支持透明背景）
- 分辨率：建议使用2倍图（@2x）
- 颜色：主色调建议使用 #1aad19（微信绿）
- 风格：简洁、现代、符合微信设计规范

## 获取图片资源

你可以：
1. 使用免费图标库（如 Iconfont、Feather Icons）
2. 自己设计制作
3. 使用AI工具生成
4. 从开源项目中获取

## 注意事项

- 确保图片版权合规
- 图片大小尽量控制在合理范围内
- 建议使用矢量图标转换为PNG
- 遵循微信小程序的设计规范
