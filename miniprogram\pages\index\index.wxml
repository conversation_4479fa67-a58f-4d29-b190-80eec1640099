<!-- 首页 -->
<view class="index-container">
  <!-- 顶部用户信息卡片 -->
  <view class="user-card" wx:if="{{userInfo}}">
    <view class="user-avatar">
      <image 
        class="avatar-img" 
        src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" 
        mode="aspectFill"
      ></image>
    </view>
    <view class="user-info">
      <text class="user-name">{{userInfo.nickname || '微信用户'}}</text>
      <text class="user-desc">欢迎使用微信登录系统</text>
    </view>
    <view class="user-status">
      <text class="status-text">已登录</text>
      <view class="status-dot"></view>
    </view>
  </view>

  <!-- 未登录提示 -->
  <view class="login-prompt" wx:if="{{!userInfo}}">
    <image class="prompt-icon" src="/images/login-prompt.png"></image>
    <text class="prompt-title">请先登录</text>
    <text class="prompt-desc">登录后即可使用完整功能</text>
    <button class="prompt-btn" bindtap="goToLogin">立即登录</button>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section" wx:if="{{userInfo}}">
    <view class="section-title">功能菜单</view>
    <view class="menu-grid">
      <view class="menu-item" bindtap="goToProfile">
        <image class="menu-icon" src="/images/profile-menu.png"></image>
        <text class="menu-text">个人中心</text>
      </view>
      <view class="menu-item" bindtap="showUserInfo">
        <image class="menu-icon" src="/images/info-menu.png"></image>
        <text class="menu-text">用户信息</text>
      </view>
      <view class="menu-item" bindtap="checkToken">
        <image class="menu-icon" src="/images/token-menu.png"></image>
        <text class="menu-text">Token状态</text>
      </view>
      <view class="menu-item" bindtap="refreshToken">
        <image class="menu-icon" src="/images/refresh-menu.png"></image>
        <text class="menu-text">刷新Token</text>
      </view>
    </view>
  </view>

  <!-- 系统信息 -->
  <view class="system-info">
    <view class="section-title">系统信息</view>
    <view class="info-list">
      <view class="info-item">
        <text class="info-label">小程序版本</text>
        <text class="info-value">1.0.0</text>
      </view>
      <view class="info-item">
        <text class="info-label">登录状态</text>
        <text class="info-value {{userInfo ? 'status-online' : 'status-offline'}}">
          {{userInfo ? '已登录' : '未登录'}}
        </text>
      </view>
      <view class="info-item">
        <text class="info-label">服务器状态</text>
        <text class="info-value {{serverStatus === 'online' ? 'status-online' : 'status-offline'}}">
          {{serverStatus === 'online' ? '正常' : '离线'}}
        </text>
      </view>
      <view class="info-item">
        <text class="info-label">最后更新</text>
        <text class="info-value">{{lastUpdateTime}}</text>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="quick-actions" wx:if="{{userInfo}}">
    <view class="section-title">快速操作</view>
    <view class="action-list">
      <button class="action-btn primary" bindtap="updateUserInfo">
        更新用户信息
      </button>
      <button class="action-btn secondary" bindtap="clearCache">
        清除缓存
      </button>
      <button class="action-btn danger" bindtap="logout">
        退出登录
      </button>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer-info">
    <text class="footer-text">微信登录系统演示</text>
    <text class="footer-text">技术栈：微信小程序 + Node.js + MySQL</text>
  </view>
</view>
