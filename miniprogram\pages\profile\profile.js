// 个人中心页面逻辑
const auth = require('../../utils/auth');
const api = require('../../utils/api');

Page({
  data: {
    userInfo: null,
    genderText: '未知',
    locationText: '未知',
    registerTime: '未知',
    lastLoginTime: '未知',
    showNicknameModal: false,
    newNickname: ''
  },

  onLoad: function (options) {
    console.log('个人中心页面加载', options);
    
    // 检查登录状态
    if (!auth.requireLogin()) {
      return;
    }
    
    this.initPage();
  },

  onShow: function () {
    console.log('个人中心页面显示');
    this.refreshUserInfo();
  },

  onReady: function () {
    console.log('个人中心页面渲染完成');
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新');
    this.refreshUserInfo().finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 初始化页面
   */
  initPage: function () {
    this.refreshUserInfo();
  },

  /**
   * 刷新用户信息
   */
  refreshUserInfo: function () {
    return new Promise((resolve) => {
      const userInfo = auth.getUserInfo();
      if (userInfo) {
        this.setUserInfo(userInfo);
      }

      // 从服务器获取最新信息
      auth.getCurrentUser()
        .then(result => {
          if (result.code === 200) {
            this.setUserInfo(result.data.user);
            
            // 更新本地存储
            const app = getApp();
            app.setLoginStatus(app.getToken(), result.data.user);
          }
          resolve();
        })
        .catch(error => {
          console.error('获取用户信息失败:', error);
          resolve();
        });
    });
  },

  /**
   * 设置用户信息到页面数据
   */
  setUserInfo: function (userInfo) {
    this.setData({
      userInfo: userInfo,
      genderText: this.getGenderText(userInfo.gender),
      locationText: this.getLocationText(userInfo),
      registerTime: this.formatTime(userInfo.created_at),
      lastLoginTime: this.formatTime(userInfo.last_login_time)
    });
  },

  /**
   * 更换头像
   */
  changeAvatar: function () {
    wx.showActionSheet({
      itemList: ['从相册选择', '拍照'],
      success: (res) => {
        const sourceType = res.tapIndex === 0 ? ['album'] : ['camera'];
        
        wx.chooseImage({
          count: 1,
          sizeType: ['compressed'],
          sourceType: sourceType,
          success: (res) => {
            const tempFilePath = res.tempFilePaths[0];
            this.uploadAvatar(tempFilePath);
          },
          fail: (error) => {
            console.error('选择图片失败:', error);
            wx.showToast({
              title: '选择图片失败',
              icon: 'error'
            });
          }
        });
      }
    });
  },

  /**
   * 上传头像
   */
  uploadAvatar: function (filePath) {
    wx.showLoading({
      title: '上传中...'
    });

    // 这里应该调用后端的头像上传接口
    // 由于示例中没有实现文件上传，这里只是模拟
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟上传成功，更新本地显示
      const userInfo = { ...this.data.userInfo };
      userInfo.avatar_url = filePath; // 实际应该是服务器返回的URL
      
      this.setData({
        userInfo: userInfo
      });
      
      wx.showToast({
        title: '头像更新成功',
        icon: 'success'
      });
    }, 2000);
  },

  /**
   * 编辑昵称
   */
  editNickname: function () {
    this.setData({
      showNicknameModal: true,
      newNickname: this.data.userInfo.nickname || ''
    });
  },

  /**
   * 昵称输入
   */
  onNicknameInput: function (e) {
    this.setData({
      newNickname: e.detail.value
    });
  },

  /**
   * 关闭昵称编辑弹窗
   */
  closeNicknameModal: function () {
    this.setData({
      showNicknameModal: false,
      newNickname: ''
    });
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation: function () {
    // 阻止点击弹窗内容时关闭弹窗
  },

  /**
   * 保存昵称
   */
  saveNickname: function () {
    const newNickname = this.data.newNickname.trim();
    
    if (!newNickname) {
      wx.showToast({
        title: '昵称不能为空',
        icon: 'error'
      });
      return;
    }

    if (newNickname.length > 20) {
      wx.showToast({
        title: '昵称长度不能超过20个字符',
        icon: 'error'
      });
      return;
    }

    wx.showLoading({
      title: '保存中...'
    });

    // 这里应该调用后端的用户信息更新接口
    // 由于示例中没有实现，这里只是模拟
    setTimeout(() => {
      wx.hideLoading();
      
      // 模拟更新成功
      const userInfo = { ...this.data.userInfo };
      userInfo.nickname = newNickname;
      
      this.setData({
        userInfo: userInfo,
        showNicknameModal: false,
        newNickname: ''
      });
      
      // 更新本地存储
      const app = getApp();
      app.setLoginStatus(app.getToken(), userInfo);
      
      wx.showToast({
        title: '昵称更新成功',
        icon: 'success'
      });
    }, 1500);
  },

  /**
   * 显示Token信息
   */
  showTokenInfo: function () {
    wx.showLoading({
      title: '获取中...'
    });

    auth.checkToken()
      .then(result => {
        wx.hideLoading();
        
        if (result.code === 200) {
          const expiresAt = new Date(result.data.expiresAt);
          const now = new Date();
          const remainingTime = Math.floor((expiresAt - now) / 1000 / 60); // 分钟
          
          const content = `Token状态: 有效\n过期时间: ${expiresAt.toLocaleString()}\n剩余时间: ${remainingTime > 0 ? remainingTime + '分钟' : '即将过期'}`;

          wx.showModal({
            title: 'Token信息',
            content: content,
            showCancel: false,
            confirmText: '我知道了',
            confirmColor: '#1aad19'
          });
        } else {
          wx.showModal({
            title: 'Token信息',
            content: 'Token无效或已过期',
            showCancel: false,
            confirmText: '我知道了',
            confirmColor: '#1aad19'
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        wx.showToast({
          title: '获取失败',
          icon: 'error'
        });
      });
  },

  /**
   * 导出用户数据
   */
  exportUserData: function () {
    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息不存在',
        icon: 'error'
      });
      return;
    }

    const data = {
      用户ID: userInfo.id,
      昵称: userInfo.nickname || '未设置',
      性别: this.data.genderText,
      地区: this.data.locationText,
      手机号: userInfo.phone || '未绑定',
      邮箱: userInfo.email || '未绑定',
      注册时间: this.data.registerTime,
      最后登录: this.data.lastLoginTime,
      登录IP: userInfo.last_login_ip || '未知',
      账户状态: userInfo.status === 1 ? '正常' : '已禁用'
    };

    const content = Object.keys(data).map(key => `${key}: ${data[key]}`).join('\n');

    wx.showModal({
      title: '用户数据',
      content: content,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '复制',
      confirmColor: '#1aad19',
      success: (res) => {
        if (res.confirm) {
          wx.setClipboardData({
            data: content,
            success: () => {
              wx.showToast({
                title: '已复制到剪贴板',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  },

  /**
   * 退出登录
   */
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...'
          });

          auth.logout()
            .then(result => {
              wx.hideLoading();
              
              wx.showToast({
                title: '退出成功',
                icon: 'success',
                duration: 1500
              });

              // 跳转到登录页
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/login/login'
                });
              }, 1500);
            })
            .catch(error => {
              wx.hideLoading();
              wx.showToast({
                title: '退出失败',
                icon: 'error'
              });
            });
        }
      }
    });
  },

  /**
   * 注销账户
   */
  deleteAccount: function () {
    wx.showModal({
      title: '注销账户',
      content: '注销账户将永久删除您的所有数据，此操作不可恢复。确定要继续吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定注销',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '功能开发中',
            icon: 'none'
          });
        }
      }
    });
  },

  /**
   * 获取性别文本
   */
  getGenderText: function (gender) {
    switch (gender) {
      case 1: return '男';
      case 2: return '女';
      default: return '未知';
    }
  },

  /**
   * 获取地区文本
   */
  getLocationText: function (userInfo) {
    if (!userInfo) return '未知';
    
    const parts = [];
    if (userInfo.country) parts.push(userInfo.country);
    if (userInfo.province) parts.push(userInfo.province);
    if (userInfo.city) parts.push(userInfo.city);
    return parts.length > 0 ? parts.join(' ') : '未知';
  },

  /**
   * 格式化时间
   */
  formatTime: function (timeString) {
    if (!timeString) return '未知';
    
    const date = new Date(timeString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: '微信登录系统',
      desc: '安全便捷的小程序登录体验',
      path: '/pages/index/index'
    };
  },

  /**
   * 页面分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '微信登录系统 - 安全便捷的小程序登录体验'
    };
  }
});
