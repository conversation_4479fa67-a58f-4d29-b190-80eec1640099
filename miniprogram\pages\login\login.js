// 登录页面逻辑
const auth = require('../../utils/auth');

Page({
  data: {
    isLoading: false
  },

  onLoad: function (options) {
    console.log('登录页面加载', options);
    
    // 检查是否已经登录
    if (auth.isLoggedIn()) {
      console.log('用户已登录，跳转到主页');
      auth.handleLoginSuccess();
      return;
    }

    // 尝试自动登录
    this.tryAutoLogin();
  },

  onShow: function () {
    console.log('登录页面显示');
  },

  onReady: function () {
    console.log('登录页面渲染完成');
  },

  /**
   * 尝试自动登录
   */
  tryAutoLogin: function () {
    console.log('尝试自动登录...');
    
    auth.autoLogin()
      .then(result => {
        console.log('自动登录成功:', result);
        
        wx.showToast({
          title: '自动登录成功',
          icon: 'success',
          duration: 1500
        });

        setTimeout(() => {
          auth.handleLoginSuccess();
        }, 1500);
      })
      .catch(error => {
        console.log('自动登录失败:', error.message);
        // 自动登录失败是正常情况，不需要提示用户
      });
  },

  /**
   * 处理登录按钮点击
   */
  handleLogin: function () {
    if (this.data.isLoading) {
      return;
    }

    console.log('用户点击登录按钮');

    this.setData({
      isLoading: true
    });

    // 调用微信登录
    auth.wechatLogin()
      .then(result => {
        console.log('登录成功:', result);
        
        this.setData({
          isLoading: false
        });

        // 显示登录成功提示
        const message = result.isNewUser ? '注册成功，欢迎使用！' : '登录成功，欢迎回来！';
        wx.showToast({
          title: message,
          icon: 'success',
          duration: 2000
        });

        // 延迟跳转，让用户看到成功提示
        setTimeout(() => {
          auth.handleLoginSuccess();
        }, 2000);
      })
      .catch(error => {
        console.error('登录失败:', error);
        
        this.setData({
          isLoading: false
        });

        // 显示错误提示
        let message = '登录失败，请重试';
        
        if (error.message) {
          if (error.message.includes('网络')) {
            message = '网络连接失败，请检查网络后重试';
          } else if (error.message.includes('频繁')) {
            message = '登录请求过于频繁，请稍后再试';
          } else if (error.message.includes('凭证')) {
            message = '登录凭证无效，请重新尝试';
          } else {
            message = error.message;
          }
        }

        wx.showModal({
          title: '登录失败',
          content: message,
          showCancel: false,
          confirmText: '我知道了',
          confirmColor: '#1aad19'
        });
      });
  },

  /**
   * 显示用户协议
   */
  showUserAgreement: function () {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的内容。在实际项目中，您应该提供完整的用户协议文本或跳转到协议页面。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '我同意',
      confirmColor: '#1aad19'
    });
  },

  /**
   * 显示隐私政策
   */
  showPrivacyPolicy: function () {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的内容。在实际项目中，您应该提供完整的隐私政策文本或跳转到政策页面。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '我同意',
      confirmColor: '#1aad19'
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: '微信登录系统',
      desc: '安全便捷的小程序登录体验',
      path: '/pages/login/login'
    };
  },

  /**
   * 页面分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '微信登录系统 - 安全便捷的小程序登录体验'
    };
  }
});
