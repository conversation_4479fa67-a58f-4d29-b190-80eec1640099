{"name": "wechat-miniprogram-server", "version": "1.0.0", "description": "微信小程序登录系统后端服务器", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "init-db": "node scripts/init-database.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["wechat", "miniprogram", "login", "nodejs", "express", "mysql"], "author": "Your Name", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.0", "axios": "^1.5.0", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-rate-limit": "^6.10.0", "helmet": "^7.0.0", "joi": "^17.9.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}