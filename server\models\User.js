// 用户数据模型
const { query, transaction } = require('../config/database');

class User {
  constructor(data = {}) {
    this.id = data.id;
    this.openid = data.openid;
    this.unionid = data.unionid;
    this.nickname = data.nickname;
    this.avatar_url = data.avatar_url;
    this.gender = data.gender;
    this.country = data.country;
    this.province = data.province;
    this.city = data.city;
    this.language = data.language;
    this.phone = data.phone;
    this.email = data.email;
    this.status = data.status;
    this.last_login_time = data.last_login_time;
    this.last_login_ip = data.last_login_ip;
    this.created_at = data.created_at;
    this.updated_at = data.updated_at;
  }

  // 根据openid查找用户
  static async findByOpenId(openid) {
    const sql = 'SELECT * FROM users WHERE openid = ? AND status = 1';
    const result = await query(sql, [openid]);
    
    if (result.success && result.data.length > 0) {
      return new User(result.data[0]);
    }
    return null;
  }

  // 根据用户ID查找用户
  static async findById(id) {
    const sql = 'SELECT * FROM users WHERE id = ? AND status = 1';
    const result = await query(sql, [id]);
    
    if (result.success && result.data.length > 0) {
      return new User(result.data[0]);
    }
    return null;
  }

  // 创建新用户
  static async create(userData) {
    const sql = `
      INSERT INTO users (openid, unionid, nickname, avatar_url, gender, 
                        country, province, city, language, last_login_time, last_login_ip)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), ?)
    `;
    
    const params = [
      userData.openid,
      userData.unionid || null,
      userData.nickname || null,
      userData.avatar_url || null,
      userData.gender || 0,
      userData.country || null,
      userData.province || null,
      userData.city || null,
      userData.language || null,
      userData.ip || null
    ];

    const result = await query(sql, params);
    
    if (result.success) {
      return await User.findById(result.data.insertId);
    }
    
    throw new Error('创建用户失败: ' + result.error);
  }

  // 更新用户信息
  async update(updateData) {
    const fields = [];
    const values = [];
    
    // 动态构建更新字段
    const allowedFields = ['nickname', 'avatar_url', 'gender', 'country', 'province', 'city', 'language', 'phone', 'email'];
    
    allowedFields.forEach(field => {
      if (updateData.hasOwnProperty(field)) {
        fields.push(`${field} = ?`);
        values.push(updateData[field]);
      }
    });

    if (fields.length === 0) {
      return this;
    }

    fields.push('updated_at = NOW()');
    values.push(this.id);

    const sql = `UPDATE users SET ${fields.join(', ')} WHERE id = ?`;
    const result = await query(sql, values);
    
    if (result.success) {
      return await User.findById(this.id);
    }
    
    throw new Error('更新用户信息失败: ' + result.error);
  }

  // 更新最后登录信息
  async updateLastLogin(ip) {
    const sql = 'UPDATE users SET last_login_time = NOW(), last_login_ip = ? WHERE id = ?';
    const result = await query(sql, [ip, this.id]);
    
    if (!result.success) {
      console.error('更新最后登录信息失败:', result.error);
    }
    
    return result.success;
  }

  // 禁用用户
  async disable() {
    const sql = 'UPDATE users SET status = 0, updated_at = NOW() WHERE id = ?';
    const result = await query(sql, [this.id]);
    
    if (result.success) {
      this.status = 0;
      return true;
    }
    
    throw new Error('禁用用户失败: ' + result.error);
  }

  // 启用用户
  async enable() {
    const sql = 'UPDATE users SET status = 1, updated_at = NOW() WHERE id = ?';
    const result = await query(sql, [this.id]);
    
    if (result.success) {
      this.status = 1;
      return true;
    }
    
    throw new Error('启用用户失败: ' + result.error);
  }

  // 获取用户的安全信息（不包含敏感数据）
  toSafeObject() {
    return {
      id: this.id,
      nickname: this.nickname,
      avatar_url: this.avatar_url,
      gender: this.gender,
      country: this.country,
      province: this.province,
      city: this.city,
      language: this.language,
      created_at: this.created_at,
      updated_at: this.updated_at
    };
  }

  // 获取用户统计信息
  static async getStats() {
    const sql = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 1 THEN 1 END) as active_users,
        COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) as today_new_users,
        COUNT(CASE WHEN DATE(last_login_time) = CURDATE() THEN 1 END) as today_active_users
      FROM users
    `;
    
    const result = await query(sql);
    
    if (result.success && result.data.length > 0) {
      return result.data[0];
    }
    
    return null;
  }
}

module.exports = User;
