/* 全局样式 */

/* 页面基础样式 */
page {
  background-color: #f8f8f8;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, 'Segoe UI', Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
  font-size: 32rpx;
  color: #333;
  line-height: 1.6;
}

/* 容器样式 */
.container {
  padding: 40rpx;
  min-height: 100vh;
  box-sizing: border-box;
}

/* 卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

/* 按钮样式 */
.btn {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  text-align: center;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  margin: 20rpx 0;
}

.btn-primary {
  background: linear-gradient(135deg, #1aad19 0%, #2dc653 100%);
  color: #fff;
}

.btn-primary:active {
  background: linear-gradient(135deg, #179b16 0%, #28b249 100%);
}

.btn-secondary {
  background-color: #f8f8f8;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.btn-secondary:active {
  background-color: #e8e8e8;
}

.btn-danger {
  background-color: #ff4757;
  color: #fff;
}

.btn-danger:active {
  background-color: #ff3838;
}

.btn-disabled {
  background-color: #f5f5f5 !important;
  color: #ccc !important;
  cursor: not-allowed;
}

/* 输入框样式 */
.input {
  width: 100%;
  height: 88rpx;
  padding: 0 30rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 32rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.input:focus {
  border-color: #1aad19;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

/* 文本样式 */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: #1aad19;
}

.text-secondary {
  color: #666;
}

.text-danger {
  color: #ff4757;
}

.text-success {
  color: #2ed573;
}

.text-warning {
  color: #ffa502;
}

.text-small {
  font-size: 24rpx;
}

.text-large {
  font-size: 36rpx;
}

.text-bold {
  font-weight: 600;
}

/* 间距样式 */
.mt-10 { margin-top: 20rpx; }
.mt-20 { margin-top: 40rpx; }
.mt-30 { margin-top: 60rpx; }

.mb-10 { margin-bottom: 20rpx; }
.mb-20 { margin-bottom: 40rpx; }
.mb-30 { margin-bottom: 60rpx; }

.ml-10 { margin-left: 20rpx; }
.ml-20 { margin-left: 40rpx; }

.mr-10 { margin-right: 20rpx; }
.mr-20 { margin-right: 40rpx; }

.pt-10 { padding-top: 20rpx; }
.pt-20 { padding-top: 40rpx; }
.pt-30 { padding-top: 60rpx; }

.pb-10 { padding-bottom: 20rpx; }
.pb-20 { padding-bottom: 40rpx; }
.pb-30 { padding-bottom: 60rpx; }

.pl-10 { padding-left: 20rpx; }
.pl-20 { padding-left: 40rpx; }

.pr-10 { padding-right: 20rpx; }
.pr-20 { padding-right: 40rpx; }

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 头像样式 */
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
}

.avatar-large {
  width: 160rpx;
  height: 160rpx;
}

.avatar-small {
  width: 80rpx;
  height: 80rpx;
}

/* 分割线 */
.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 40rpx 0;
}

/* 空状态 */
.empty {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}

/* 加载状态 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 错误状态 */
.error {
  text-align: center;
  padding: 40rpx;
  color: #ff4757;
}

/* 成功状态 */
.success {
  text-align: center;
  padding: 40rpx;
  color: #2ed573;
}
