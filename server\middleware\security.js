// 安全中间件
const crypto = require('crypto');

/**
 * 请求ID生成中间件
 * 为每个请求生成唯一ID，用于日志追踪
 */
function requestId(req, res, next) {
  req.requestId = crypto.randomUUID();
  res.setHeader('X-Request-ID', req.requestId);
  next();
}

/**
 * IP白名单中间件
 * @param {Array} allowedIPs - 允许的IP地址列表
 * @returns {Function} 中间件函数
 */
function ipWhitelist(allowedIPs = []) {
  return (req, res, next) => {
    if (allowedIPs.length === 0) {
      return next();
    }

    const clientIP = req.ip || req.connection.remoteAddress || req.headers['x-forwarded-for'];
    
    if (!allowedIPs.includes(clientIP)) {
      console.warn(`IP访问被拒绝: ${clientIP}`);
      return res.status(403).json({
        code: 403,
        message: '访问被拒绝',
        data: null
      });
    }

    next();
  };
}

/**
 * 请求大小限制中间件
 * @param {Number} maxSize - 最大请求大小（字节）
 * @returns {Function} 中间件函数
 */
function requestSizeLimit(maxSize = 10 * 1024 * 1024) { // 10MB
  return (req, res, next) => {
    const contentLength = parseInt(req.headers['content-length'] || '0');
    
    if (contentLength > maxSize) {
      return res.status(413).json({
        code: 413,
        message: '请求体过大',
        data: null
      });
    }

    next();
  };
}

/**
 * 防止暴力破解中间件
 * @param {Object} options - 配置选项
 * @returns {Function} 中间件函数
 */
function bruteForceProtection(options = {}) {
  const {
    maxAttempts = 5,
    windowMs = 15 * 60 * 1000, // 15分钟
    blockDuration = 60 * 60 * 1000, // 1小时
    skipSuccessfulRequests = true
  } = options;

  const attempts = new Map();

  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    
    // 清理过期记录
    for (const [ip, data] of attempts.entries()) {
      if (now - data.firstAttempt > windowMs) {
        attempts.delete(ip);
      }
    }

    const attemptData = attempts.get(key) || {
      count: 0,
      firstAttempt: now,
      blockedUntil: 0
    };

    // 检查是否仍在封禁期
    if (attemptData.blockedUntil > now) {
      const remainingTime = Math.ceil((attemptData.blockedUntil - now) / 1000 / 60);
      return res.status(429).json({
        code: 429,
        message: `IP已被临时封禁，请${remainingTime}分钟后再试`,
        data: null
      });
    }

    // 重置封禁状态
    if (attemptData.blockedUntil > 0 && attemptData.blockedUntil <= now) {
      attemptData.count = 0;
      attemptData.firstAttempt = now;
      attemptData.blockedUntil = 0;
    }

    // 检查是否超过尝试次数
    if (attemptData.count >= maxAttempts) {
      attemptData.blockedUntil = now + blockDuration;
      attempts.set(key, attemptData);
      
      const blockMinutes = Math.ceil(blockDuration / 1000 / 60);
      return res.status(429).json({
        code: 429,
        message: `尝试次数过多，IP已被封禁${blockMinutes}分钟`,
        data: null
      });
    }

    // 记录尝试
    const originalSend = res.send;
    res.send = function(data) {
      const statusCode = res.statusCode;
      
      // 根据响应状态码判断是否为失败请求
      if (statusCode >= 400 && statusCode < 500) {
        attemptData.count++;
        attempts.set(key, attemptData);
      } else if (skipSuccessfulRequests && statusCode < 400) {
        // 成功请求，重置计数
        attempts.delete(key);
      }
      
      originalSend.call(this, data);
    };

    next();
  };
}

/**
 * 请求签名验证中间件
 * @param {String} secretKey - 签名密钥
 * @returns {Function} 中间件函数
 */
function signatureVerification(secretKey) {
  return (req, res, next) => {
    const signature = req.headers['x-signature'];
    const timestamp = req.headers['x-timestamp'];
    const nonce = req.headers['x-nonce'];

    if (!signature || !timestamp || !nonce) {
      return res.status(401).json({
        code: 401,
        message: '缺少签名信息',
        data: null
      });
    }

    // 检查时间戳（防重放攻击）
    const now = Date.now();
    const requestTime = parseInt(timestamp);
    const timeDiff = Math.abs(now - requestTime);
    
    if (timeDiff > 5 * 60 * 1000) { // 5分钟
      return res.status(401).json({
        code: 401,
        message: '请求时间戳无效',
        data: null
      });
    }

    // 生成签名
    const method = req.method;
    const url = req.originalUrl;
    const body = JSON.stringify(req.body || {});
    const signString = `${method}${url}${body}${timestamp}${nonce}`;
    
    const expectedSignature = crypto
      .createHmac('sha256', secretKey)
      .update(signString)
      .digest('hex');

    if (signature !== expectedSignature) {
      return res.status(401).json({
        code: 401,
        message: '签名验证失败',
        data: null
      });
    }

    next();
  };
}

/**
 * 敏感信息过滤中间件
 * 过滤响应中的敏感信息
 */
function sensitiveDataFilter(req, res, next) {
  const originalSend = res.send;
  
  res.send = function(data) {
    try {
      if (typeof data === 'string') {
        const jsonData = JSON.parse(data);
        const filteredData = filterSensitiveData(jsonData);
        data = JSON.stringify(filteredData);
      } else if (typeof data === 'object') {
        data = filterSensitiveData(data);
      }
    } catch (error) {
      // 如果不是JSON数据，直接返回
    }
    
    originalSend.call(this, data);
  };

  next();
}

/**
 * 过滤敏感数据
 * @param {Object} data - 要过滤的数据
 * @returns {Object} 过滤后的数据
 */
function filterSensitiveData(data) {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const sensitiveFields = [
    'password',
    'session_key',
    'openid', // 在某些情况下可能需要过滤
    'access_token',
    'refresh_token',
    'secret',
    'private_key'
  ];

  const filtered = Array.isArray(data) ? [] : {};

  for (const key in data) {
    if (data.hasOwnProperty(key)) {
      if (sensitiveFields.includes(key.toLowerCase())) {
        // 敏感字段用星号替换
        filtered[key] = '***';
      } else if (typeof data[key] === 'object' && data[key] !== null) {
        // 递归过滤嵌套对象
        filtered[key] = filterSensitiveData(data[key]);
      } else {
        filtered[key] = data[key];
      }
    }
  }

  return filtered;
}

/**
 * CSRF保护中间件
 * @param {Object} options - 配置选项
 * @returns {Function} 中间件函数
 */
function csrfProtection(options = {}) {
  const { 
    headerName = 'x-csrf-token',
    cookieName = 'csrf-token',
    ignoreMethods = ['GET', 'HEAD', 'OPTIONS']
  } = options;

  return (req, res, next) => {
    // 跳过安全方法
    if (ignoreMethods.includes(req.method)) {
      return next();
    }

    const token = req.headers[headerName] || req.body._csrf;
    const cookieToken = req.cookies && req.cookies[cookieName];

    if (!token || !cookieToken || token !== cookieToken) {
      return res.status(403).json({
        code: 403,
        message: 'CSRF token验证失败',
        data: null
      });
    }

    next();
  };
}

module.exports = {
  requestId,
  ipWhitelist,
  requestSizeLimit,
  bruteForceProtection,
  signatureVerification,
  sensitiveDataFilter,
  csrfProtection,
  filterSensitiveData
};
