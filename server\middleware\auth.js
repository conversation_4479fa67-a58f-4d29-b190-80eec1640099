// 身份验证中间件
const { verifyToken, extractTokenFromRequest } = require('../utils/jwt');
const User = require('../models/User');

/**
 * JWT身份验证中间件
 * 验证请求中的JWT Token，并将用户信息添加到req.user
 */
async function authenticateToken(req, res, next) {
  try {
    // 提取Token
    const token = extractTokenFromRequest(req);
    
    if (!token) {
      return res.status(401).json({
        code: 401,
        message: '缺少访问令牌',
        data: null
      });
    }

    // 验证Token
    const decoded = verifyToken(token);
    
    // 查找用户
    const user = await User.findById(decoded.userId);
    
    if (!user) {
      return res.status(401).json({
        code: 401,
        message: '用户不存在或已被禁用',
        data: null
      });
    }

    // 检查用户状态
    if (user.status !== 1) {
      return res.status(401).json({
        code: 401,
        message: '用户账号已被禁用',
        data: null
      });
    }

    // 将用户信息添加到请求对象
    req.user = user;
    req.token = token;
    req.tokenPayload = decoded;

    next();
  } catch (error) {
    console.error('Token验证失败:', error.message);
    
    let message = '身份验证失败';
    let code = 401;
    
    if (error.message === 'Token已过期') {
      message = 'Token已过期，请重新登录';
      code = 401;
    } else if (error.message === 'Token无效') {
      message = 'Token无效，请重新登录';
      code = 401;
    }

    return res.status(code).json({
      code: code,
      message: message,
      data: null
    });
  }
}

/**
 * 可选的身份验证中间件
 * 如果有Token则验证，没有Token则跳过
 */
async function optionalAuth(req, res, next) {
  try {
    const token = extractTokenFromRequest(req);
    
    if (token) {
      const decoded = verifyToken(token);
      const user = await User.findById(decoded.userId);
      
      if (user && user.status === 1) {
        req.user = user;
        req.token = token;
        req.tokenPayload = decoded;
      }
    }

    next();
  } catch (error) {
    // 可选验证失败时不阻止请求继续
    console.warn('可选身份验证失败:', error.message);
    next();
  }
}

/**
 * 管理员权限验证中间件
 * 需要先通过authenticateToken中间件
 */
function requireAdmin(req, res, next) {
  if (!req.user) {
    return res.status(401).json({
      code: 401,
      message: '需要身份验证',
      data: null
    });
  }

  // 这里可以根据实际需求添加管理员权限判断逻辑
  // 例如检查用户角色、权限等
  if (req.user.role !== 'admin') {
    return res.status(403).json({
      code: 403,
      message: '权限不足',
      data: null
    });
  }

  next();
}

/**
 * 检查用户是否为资源所有者
 * @param {String} userIdField - 请求参数中用户ID的字段名
 */
function requireOwnership(userIdField = 'userId') {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        code: 401,
        message: '需要身份验证',
        data: null
      });
    }

    const resourceUserId = req.params[userIdField] || req.body[userIdField];
    
    if (!resourceUserId) {
      return res.status(400).json({
        code: 400,
        message: '缺少用户ID参数',
        data: null
      });
    }

    if (req.user.id.toString() !== resourceUserId.toString()) {
      return res.status(403).json({
        code: 403,
        message: '只能访问自己的资源',
        data: null
      });
    }

    next();
  };
}

/**
 * 限制请求频率的中间件
 * @param {Number} maxRequests - 最大请求次数
 * @param {Number} windowMs - 时间窗口（毫秒）
 */
function rateLimit(maxRequests = 100, windowMs = 15 * 60 * 1000) {
  const requests = new Map();

  return (req, res, next) => {
    const key = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    
    if (!requests.has(key)) {
      requests.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const requestInfo = requests.get(key);
    
    if (now > requestInfo.resetTime) {
      requestInfo.count = 1;
      requestInfo.resetTime = now + windowMs;
      return next();
    }

    if (requestInfo.count >= maxRequests) {
      return res.status(429).json({
        code: 429,
        message: '请求过于频繁，请稍后再试',
        data: null
      });
    }

    requestInfo.count++;
    next();
  };
}

module.exports = {
  authenticateToken,
  optionalAuth,
  requireAdmin,
  requireOwnership,
  rateLimit
};
