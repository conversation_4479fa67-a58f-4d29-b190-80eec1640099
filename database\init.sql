-- 微信小程序登录系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wechat_miniprogram 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE wechat_miniprogram;

-- 创建用户表
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户ID',
    openid VARCHAR(100) NOT NULL UNIQUE COMMENT '微信用户唯一标识',
    unionid VARCHAR(100) DEFAULT NULL COMMENT '微信开放平台唯一标识',
    nickname VARCHAR(100) DEFAULT NULL COMMENT '用户昵称',
    avatar_url VARCHAR(500) DEFAULT NULL COMMENT '用户头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别：0-未知，1-男，2-女',
    country VARCHAR(50) DEFAULT NULL COMMENT '国家',
    province VARCHAR(50) DEFAULT NULL COMMENT '省份',
    city VARCHAR(50) DEFAULT NULL COMMENT '城市',
    language VARCHAR(20) DEFAULT NULL COMMENT '语言',
    phone VARCHAR(20) DEFAULT NULL COMMENT '手机号',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    status TINYINT DEFAULT 1 COMMENT '用户状态：0-禁用，1-正常',
    last_login_time DATETIME DEFAULT NULL COMMENT '最后登录时间',
    last_login_ip VARCHAR(50) DEFAULT NULL COMMENT '最后登录IP',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_openid (openid),
    INDEX idx_unionid (unionid),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建用户会话表（可选，用于管理用户登录会话）
CREATE TABLE IF NOT EXISTS user_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '会话ID',
    user_id INT NOT NULL COMMENT '用户ID',
    session_key VARCHAR(100) NOT NULL COMMENT '微信会话密钥',
    access_token VARCHAR(500) NOT NULL COMMENT '访问令牌',
    refresh_token VARCHAR(500) DEFAULT NULL COMMENT '刷新令牌',
    expires_at DATETIME NOT NULL COMMENT '过期时间',
    device_info VARCHAR(200) DEFAULT NULL COMMENT '设备信息',
    ip_address VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    is_active TINYINT DEFAULT 1 COMMENT '是否活跃：0-否，1-是',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_access_token (access_token(100)),
    INDEX idx_expires_at (expires_at),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 创建系统日志表（可选，用于记录重要操作）
CREATE TABLE IF NOT EXISTS system_logs (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '日志ID',
    user_id INT DEFAULT NULL COMMENT '用户ID',
    action VARCHAR(100) NOT NULL COMMENT '操作类型',
    description TEXT DEFAULT NULL COMMENT '操作描述',
    ip_address VARCHAR(50) DEFAULT NULL COMMENT 'IP地址',
    user_agent VARCHAR(500) DEFAULT NULL COMMENT '用户代理',
    request_data JSON DEFAULT NULL COMMENT '请求数据',
    response_data JSON DEFAULT NULL COMMENT '响应数据',
    status TINYINT DEFAULT 1 COMMENT '状态：0-失败，1-成功',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_user_id (user_id),
    INDEX idx_action (action),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统日志表';

-- 插入测试数据（可选）
-- INSERT INTO users (openid, nickname, avatar_url, gender) VALUES 
-- ('test_openid_001', '测试用户1', 'https://example.com/avatar1.jpg', 1),
-- ('test_openid_002', '测试用户2', 'https://example.com/avatar2.jpg', 2);

-- 显示创建的表
SHOW TABLES;

-- 显示用户表结构
DESCRIBE users;
