/* 个人中心页面样式 */

.profile-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: 40rpx;
}

/* 用户头像和基本信息 */
.profile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 80rpx;
  text-align: center;
  position: relative;
}

.avatar-section {
  position: relative;
  display: inline-block;
  margin-bottom: 40rpx;
}

.user-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 6rpx solid rgba(255, 255, 255, 0.3);
}

.avatar-edit-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 60rpx;
  height: 60rpx;
  background-color: #1aad19;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 4rpx solid #fff;
}

.edit-icon {
  width: 30rpx;
  height: 30rpx;
}

.user-basic-info {
  color: #fff;
}

.user-nickname {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.user-id {
  display: block;
  font-size: 26rpx;
  opacity: 0.8;
}

/* 信息区域 */
.info-section,
.account-section,
.menu-section,
.danger-section {
  background-color: #fff;
  margin: 20rpx 40rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  padding: 40rpx 40rpx 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

.info-value-container {
  display: flex;
  align-items: center;
}

.info-value {
  font-size: 28rpx;
  color: #666;
  margin-right: 10rpx;
}

.status-active {
  color: #2ed573;
}

.status-inactive {
  color: #ff4757;
}

.arrow-icon {
  width: 24rpx;
  height: 24rpx;
  opacity: 0.5;
}

/* 菜单项 */
.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx 40rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 30rpx;
}

.menu-text {
  flex: 1;
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
}

/* 危险操作 */
.danger-section {
  margin-top: 40rpx;
}

.danger-btn {
  width: calc(100% - 80rpx);
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  margin: 20rpx 40rpx;
  background-color: #ff4757;
  color: #fff;
}

.danger-btn.secondary {
  background-color: #f8f8f8;
  color: #ff4757;
  border: 2rpx solid #ff4757;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background-color: #fff;
  border-radius: 20rpx;
  width: 600rpx;
  max-width: 90vw;
  overflow: hidden;
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
}

.modal-body {
  padding: 40rpx;
}

.nickname-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e5e5e5;
  border-radius: 8rpx;
  font-size: 30rpx;
  background-color: #fff;
  box-sizing: border-box;
}

.nickname-input:focus {
  border-color: #1aad19;
}

.input-tip {
  display: block;
  font-size: 24rpx;
  color: #999;
  margin-top: 20rpx;
}

.modal-footer {
  display: flex;
  border-top: 2rpx solid #f5f5f5;
}

.modal-btn {
  flex: 1;
  height: 100rpx;
  line-height: 100rpx;
  text-align: center;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  background-color: #fff;
}

.modal-btn.cancel {
  color: #666;
  border-right: 2rpx solid #f5f5f5;
}

.modal-btn.confirm {
  color: #1aad19;
}
