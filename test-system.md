# 系统测试指南

## 🧪 测试步骤

### 1. 测试数据库连接

```bash
cd server
node scripts/init-database.js
```

**预期结果**：
- ✅ 连接到MySQL服务器成功
- ✅ 数据库初始化完成
- 📋 显示创建的表列表

### 2. 启动后端服务器

```bash
cd server
npm start
```

**预期结果**：
- 🚀 服务器启动成功
- 📍 显示服务器地址
- ✅ 数据库连接测试通过

### 3. 测试API接口

#### 3.1 健康检查
```bash
curl http://localhost:3000/health
```

**预期响应**：
```json
{
  "code": 200,
  "message": "服务器运行正常",
  "data": {
    "timestamp": "2024-xx-xx",
    "uptime": 123.456,
    "environment": "development",
    "version": "1.0.0"
  }
}
```

#### 3.2 API文档
```bash
curl http://localhost:3000/api/docs
```

**预期响应**：返回API文档信息

#### 3.3 登录接口测试（需要真实的微信code）
```bash
curl -X POST http://localhost:3000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"code": "test_code"}'
```

**预期响应**：
```json
{
  "code": 400,
  "message": "登录失败，请稍后重试"
}
```
*注：由于使用的是测试code，会返回错误，这是正常的*

### 4. 测试小程序前端

#### 4.1 导入项目
1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择 `miniprogram` 目录
4. 填入你的小程序AppID

#### 4.2 配置服务器地址
编辑 `miniprogram/app.js`：
```javascript
globalData: {
  serverUrl: 'http://localhost:3000'
}
```

#### 4.3 编译运行
1. 点击"编译"按钮
2. 查看是否有编译错误
3. 检查控制台输出

### 5. 完整登录流程测试

#### 5.1 配置真实的微信小程序信息
1. 在 `server/.env` 中填入真实的AppID和AppSecret
2. 重启服务器

#### 5.2 在微信开发者工具中测试
1. 点击"登录"按钮
2. 观察网络请求
3. 检查登录是否成功

## 🔍 故障排除

### 常见错误及解决方案

#### 1. 数据库连接失败
**错误信息**：`ER_ACCESS_DENIED_ERROR`
**解决方案**：
- 检查MySQL是否启动
- 验证用户名密码
- 确认数据库权限

#### 2. 端口被占用
**错误信息**：`EADDRINUSE`
**解决方案**：
```bash
# 查找占用端口的进程
netstat -ano | findstr :3000
# 杀死进程
taskkill /PID <进程ID> /F
```

#### 3. 微信登录失败
**错误信息**：`AppID无效`
**解决方案**：
- 确认AppID和AppSecret正确
- 检查网络连接
- 验证微信小程序状态

#### 4. 跨域问题
**错误信息**：`CORS error`
**解决方案**：
- 检查CORS配置
- 确认请求域名在白名单中

## 📊 性能测试

### 1. 压力测试
```bash
# 安装压测工具
npm install -g artillery

# 创建测试配置
cat > load-test.yml << EOF
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
scenarios:
  - name: "Health check"
    requests:
      - get:
          url: "/health"
EOF

# 运行压测
artillery run load-test.yml
```

### 2. 内存监控
```bash
# 使用PM2监控
pm2 start app.js --name wechat-server
pm2 monit
```

## ✅ 测试检查清单

- [ ] 数据库连接正常
- [ ] 服务器启动成功
- [ ] 健康检查接口响应正常
- [ ] API文档可访问
- [ ] 小程序编译无错误
- [ ] 登录页面显示正常
- [ ] 首页功能正常
- [ ] 个人中心页面正常
- [ ] 网络请求正常
- [ ] 错误处理正确
- [ ] 日志记录正常

## 📝 测试报告模板

```
测试时间：2024-xx-xx
测试环境：开发环境
测试人员：xxx

功能测试结果：
✅ 数据库连接：通过
✅ 服务器启动：通过
✅ API接口：通过
✅ 小程序编译：通过
✅ 登录流程：通过
✅ 页面功能：通过

性能测试结果：
- 响应时间：< 100ms
- 并发处理：10 req/s
- 内存使用：< 100MB

发现问题：
1. 无

建议改进：
1. 添加更多测试用例
2. 完善错误处理
```

---

**测试完成后，你的微信小程序登录系统就可以正常使用了！** 🎉
