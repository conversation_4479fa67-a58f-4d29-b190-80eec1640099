// 身份验证路由
const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { authenticateToken, rateLimit } = require('../middleware/auth');
const { validateBody, schemas } = require('../middleware/validation');
const { bruteForceProtection } = require('../middleware/security');
const logger = require('../utils/logger');

// 登录防暴力破解保护
const loginBruteForce = bruteForceProtection({
  maxAttempts: 5,
  windowMs: 15 * 60 * 1000, // 15分钟
  blockDuration: 60 * 60 * 1000 // 1小时
});

/**
 * @route POST /api/auth/login
 * @desc 微信小程序登录
 * @access Public
 */
router.post('/login',
  rateLimit(10, 5 * 60 * 1000), // 5分钟内最多10次登录请求
  loginBruteForce, // 防暴力破解
  validateBody(schemas.login),
  authController.wechatLogin
);

/**
 * @route POST /api/auth/refresh
 * @desc 刷新访问令牌
 * @access Public
 */
router.post('/refresh',
  rateLimit(20, 15 * 60 * 1000), // 15分钟内最多20次刷新请求
  validateBody(schemas.refreshToken),
  authController.refreshToken
);

/**
 * @route GET /api/auth/me
 * @desc 获取当前用户信息
 * @access Private
 */
router.get('/me',
  authenticateToken,
  authController.getCurrentUser
);

/**
 * @route POST /api/auth/logout
 * @desc 用户登出
 * @access Private
 */
router.post('/logout',
  authenticateToken,
  authController.logout
);

/**
 * @route GET /api/auth/check
 * @desc 检查Token有效性
 * @access Private
 */
router.get('/check',
  authenticateToken,
  authController.checkToken
);

/**
 * @route GET /api/auth/test
 * @desc 测试接口（开发环境）
 * @access Public
 */
if (process.env.NODE_ENV === 'development') {
  router.get('/test', (req, res) => {
    res.json({
      code: 200,
      message: '身份验证模块测试成功',
      data: {
        timestamp: new Date().toISOString(),
        environment: process.env.NODE_ENV,
        version: '1.0.0'
      }
    });
  });
}

module.exports = router;
