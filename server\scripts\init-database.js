// 数据库初始化脚本
const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
const config = require('../config/config');

async function initDatabase() {
  let connection;
  
  try {
    console.log('🔄 开始初始化数据库...');
    
    // 连接到MySQL服务器（不指定数据库）
    connection = await mysql.createConnection({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password,
      charset: config.database.charset,
      multipleStatements: true
    });
    
    console.log('✅ 连接到MySQL服务器成功');
    
    // 读取SQL初始化脚本
    const sqlPath = path.join(__dirname, '../../database/init.sql');
    const sqlScript = fs.readFileSync(sqlPath, 'utf8');
    
    console.log('📄 读取SQL脚本成功');
    
    // 执行SQL脚本
    const [results] = await connection.execute(sqlScript);
    
    console.log('✅ 数据库初始化完成');
    console.log('📊 执行结果:', results);
    
    // 验证数据库和表是否创建成功
    await connection.execute(`USE ${config.database.database}`);
    const [tables] = await connection.execute('SHOW TABLES');
    
    console.log('📋 创建的表:');
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    console.log('🎉 数据库初始化成功完成!');
    
  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    
    if (error.code === 'ER_ACCESS_DENIED_ERROR') {
      console.error('💡 请检查数据库用户名和密码是否正确');
    } else if (error.code === 'ECONNREFUSED') {
      console.error('💡 请确保MySQL服务器正在运行');
    } else if (error.code === 'ER_BAD_DB_ERROR') {
      console.error('💡 数据库不存在，脚本会自动创建');
    }
    
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
