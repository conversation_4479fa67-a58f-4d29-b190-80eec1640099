/* 登录页面样式 */

.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx 40rpx;
  box-sizing: border-box;
}

/* logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 80rpx;
  padding-top: 60rpx;
}

.logo {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 40rpx;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.app-name {
  display: block;
  font-size: 48rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 20rpx;
}

.app-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

/* 登录卡片 */
.login-card {
  background-color: #fff;
  border-radius: 24rpx;
  padding: 60rpx 40rpx;
  margin-bottom: 60rpx;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.1);
}

.card-header {
  text-align: center;
  margin-bottom: 60rpx;
}

.card-title {
  display: block;
  font-size: 40rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.card-subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.card-body {
  width: 100%;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 96rpx;
  background: linear-gradient(135deg, #1aad19 0%, #2dc653 100%);
  border-radius: 48rpx;
  border: none;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 24rpx rgba(26, 173, 25, 0.3);
  transition: all 0.3s ease;
}

.login-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 16rpx rgba(26, 173, 25, 0.3);
}

.login-btn.btn-disabled {
  background: #f5f5f5;
  color: #ccc;
  box-shadow: none;
  transform: none;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.wechat-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

.btn-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #fff;
}

.btn-disabled .btn-text {
  color: #ccc;
}

/* 登录说明 */
.login-tips {
  text-align: center;
  line-height: 1.6;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.tip-link {
  font-size: 24rpx;
  color: #1aad19;
  text-decoration: underline;
}

/* 功能介绍 */
.feature-section {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 60rpx;
  backdrop-filter: blur(10rpx);
}

.feature-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #fff;
  text-align: center;
  margin-bottom: 40rpx;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.feature-item {
  display: flex;
  align-items: center;
}

.feature-icon {
  width: 60rpx;
  height: 60rpx;
  margin-right: 30rpx;
  border-radius: 12rpx;
}

.feature-content {
  flex: 1;
}

.feature-name {
  display: block;
  font-size: 28rpx;
  font-weight: 500;
  color: #fff;
  margin-bottom: 8rpx;
}

.feature-desc {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* 底部信息 */
.footer {
  text-align: center;
  margin-top: auto;
}

.footer-text {
  display: block;
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  line-height: 1.6;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 60rpx 80rpx;
  text-align: center;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.2);
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #1aad19;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}
