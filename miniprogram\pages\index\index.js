// 首页逻辑
const auth = require('../../utils/auth');
const api = require('../../utils/api');

Page({
  data: {
    userInfo: null,
    serverStatus: 'offline',
    lastUpdateTime: ''
  },

  onLoad: function (options) {
    console.log('首页加载', options);
    this.initPage();
  },

  onShow: function () {
    console.log('首页显示');
    this.refreshUserInfo();
  },

  onReady: function () {
    console.log('首页渲染完成');
  },

  onPullDownRefresh: function () {
    console.log('下拉刷新');
    this.refreshPage();
  },

  /**
   * 初始化页面
   */
  initPage: function () {
    this.refreshUserInfo();
    this.checkServerStatus();
    this.updateLastTime();
  },

  /**
   * 刷新页面数据
   */
  refreshPage: function () {
    Promise.all([
      this.refreshUserInfo(),
      this.checkServerStatus()
    ]).finally(() => {
      wx.stopPullDownRefresh();
      this.updateLastTime();
    });
  },

  /**
   * 刷新用户信息
   */
  refreshUserInfo: function () {
    return new Promise((resolve) => {
      if (auth.isLoggedIn()) {
        const userInfo = auth.getUserInfo();
        this.setData({
          userInfo: userInfo
        });
        
        // 从服务器获取最新用户信息
        auth.getCurrentUser()
          .then(result => {
            if (result.code === 200) {
              this.setData({
                userInfo: result.data.user
              });
              
              // 更新本地存储
              const app = getApp();
              app.setLoginStatus(app.getToken(), result.data.user);
            }
            resolve();
          })
          .catch(error => {
            console.error('获取用户信息失败:', error);
            resolve();
          });
      } else {
        this.setData({
          userInfo: null
        });
        resolve();
      }
    });
  },

  /**
   * 检查服务器状态
   */
  checkServerStatus: function () {
    return new Promise((resolve) => {
      api.get('/health')
        .then(result => {
          if (result.code === 200) {
            this.setData({
              serverStatus: 'online'
            });
          } else {
            this.setData({
              serverStatus: 'offline'
            });
          }
          resolve();
        })
        .catch(error => {
          console.error('检查服务器状态失败:', error);
          this.setData({
            serverStatus: 'offline'
          });
          resolve();
        });
    });
  },

  /**
   * 更新最后更新时间
   */
  updateLastTime: function () {
    const now = new Date();
    const timeString = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    this.setData({
      lastUpdateTime: timeString
    });
  },

  /**
   * 跳转到登录页
   */
  goToLogin: function () {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 跳转到个人中心
   */
  goToProfile: function () {
    if (!auth.requireLogin()) {
      return;
    }
    
    wx.switchTab({
      url: '/pages/profile/profile'
    });
  },

  /**
   * 显示用户信息
   */
  showUserInfo: function () {
    if (!auth.requireLogin()) {
      return;
    }

    const userInfo = this.data.userInfo;
    if (!userInfo) {
      wx.showToast({
        title: '用户信息不存在',
        icon: 'error'
      });
      return;
    }

    const content = `用户ID: ${userInfo.id}\n昵称: ${userInfo.nickname || '未设置'}\n性别: ${this.getGenderText(userInfo.gender)}\n地区: ${this.getLocationText(userInfo)}\n注册时间: ${this.formatTime(userInfo.created_at)}`;

    wx.showModal({
      title: '用户信息',
      content: content,
      showCancel: false,
      confirmText: '我知道了',
      confirmColor: '#1aad19'
    });
  },

  /**
   * 检查Token状态
   */
  checkToken: function () {
    if (!auth.requireLogin()) {
      return;
    }

    wx.showLoading({
      title: '检查中...'
    });

    auth.checkToken()
      .then(result => {
        wx.hideLoading();
        
        if (result.code === 200) {
          const expiresAt = new Date(result.data.expiresAt);
          const now = new Date();
          const remainingTime = Math.floor((expiresAt - now) / 1000 / 60); // 分钟

          wx.showModal({
            title: 'Token状态',
            content: `Token有效\n剩余时间: ${remainingTime > 0 ? remainingTime + '分钟' : '即将过期'}`,
            showCancel: false,
            confirmText: '我知道了',
            confirmColor: '#1aad19'
          });
        } else {
          wx.showModal({
            title: 'Token状态',
            content: 'Token无效或已过期',
            showCancel: false,
            confirmText: '重新登录',
            confirmColor: '#1aad19',
            success: (res) => {
              if (res.confirm) {
                this.logout();
              }
            }
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        wx.showToast({
          title: '检查失败',
          icon: 'error'
        });
      });
  },

  /**
   * 刷新Token
   */
  refreshToken: function () {
    if (!auth.requireLogin()) {
      return;
    }

    wx.showLoading({
      title: '刷新中...'
    });

    auth.refreshToken()
      .then(result => {
        wx.hideLoading();
        
        if (result.success) {
          wx.showToast({
            title: 'Token刷新成功',
            icon: 'success'
          });
          
          // 更新用户信息
          this.setData({
            userInfo: result.user
          });
        }
      })
      .catch(error => {
        wx.hideLoading();
        
        wx.showModal({
          title: '刷新失败',
          content: error.message || 'Token刷新失败，请重新登录',
          showCancel: true,
          cancelText: '取消',
          confirmText: '重新登录',
          confirmColor: '#1aad19',
          success: (res) => {
            if (res.confirm) {
              this.logout();
            }
          }
        });
      });
  },

  /**
   * 更新用户信息
   */
  updateUserInfo: function () {
    if (!auth.requireLogin()) {
      return;
    }

    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    });
  },

  /**
   * 清除缓存
   */
  clearCache: function () {
    wx.showModal({
      title: '清除缓存',
      content: '确定要清除本地缓存吗？这不会影响您的登录状态。',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      confirmColor: '#1aad19',
      success: (res) => {
        if (res.confirm) {
          try {
            // 保留登录相关的缓存
            const token = wx.getStorageSync('token');
            const userInfo = wx.getStorageSync('userInfo');
            const refreshToken = wx.getStorageSync('refreshToken');
            
            // 清除所有缓存
            wx.clearStorageSync();
            
            // 恢复登录相关缓存
            if (token) wx.setStorageSync('token', token);
            if (userInfo) wx.setStorageSync('userInfo', userInfo);
            if (refreshToken) wx.setStorageSync('refreshToken', refreshToken);
            
            wx.showToast({
              title: '缓存清除成功',
              icon: 'success'
            });
          } catch (error) {
            wx.showToast({
              title: '清除失败',
              icon: 'error'
            });
          }
        }
      }
    });
  },

  /**
   * 退出登录
   */
  logout: function () {
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      showCancel: true,
      cancelText: '取消',
      confirmText: '确定',
      confirmColor: '#ff4757',
      success: (res) => {
        if (res.confirm) {
          wx.showLoading({
            title: '退出中...'
          });

          auth.logout()
            .then(result => {
              wx.hideLoading();
              
              wx.showToast({
                title: '退出成功',
                icon: 'success',
                duration: 1500
              });

              // 更新页面状态
              this.setData({
                userInfo: null
              });

              // 跳转到登录页
              setTimeout(() => {
                wx.reLaunch({
                  url: '/pages/login/login'
                });
              }, 1500);
            })
            .catch(error => {
              wx.hideLoading();
              wx.showToast({
                title: '退出失败',
                icon: 'error'
              });
            });
        }
      }
    });
  },

  /**
   * 获取性别文本
   */
  getGenderText: function (gender) {
    switch (gender) {
      case 1: return '男';
      case 2: return '女';
      default: return '未知';
    }
  },

  /**
   * 获取地区文本
   */
  getLocationText: function (userInfo) {
    const parts = [];
    if (userInfo.country) parts.push(userInfo.country);
    if (userInfo.province) parts.push(userInfo.province);
    if (userInfo.city) parts.push(userInfo.city);
    return parts.length > 0 ? parts.join(' ') : '未知';
  },

  /**
   * 格式化时间
   */
  formatTime: function (timeString) {
    if (!timeString) return '未知';
    
    const date = new Date(timeString);
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    return `${year}-${month}-${day}`;
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: '微信登录系统',
      desc: '安全便捷的小程序登录体验',
      path: '/pages/index/index'
    };
  },

  /**
   * 页面分享到朋友圈
   */
  onShareTimeline: function () {
    return {
      title: '微信登录系统 - 安全便捷的小程序登录体验'
    };
  }
});
