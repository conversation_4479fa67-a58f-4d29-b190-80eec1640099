// 身份验证工具
const api = require('./api');

/**
 * 微信登录
 * @returns {Promise} 登录结果
 */
function wechatLogin() {
  return new Promise((resolve, reject) => {
    console.log('开始微信登录...');
    
    // 调用wx.login获取code
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('获取登录凭证成功:', loginRes.code);
          
          // 发送code到后端
          api.post('/api/auth/login', {
            code: loginRes.code
          })
          .then(result => {
            console.log('后端登录成功:', result);
            
            if (result.code === 200) {
              const { token, refreshToken, user, isNewUser } = result.data;
              
              // 保存登录状态
              const app = getApp();
              app.setLoginStatus(token, user);
              
              // 保存刷新token
              if (refreshToken) {
                wx.setStorageSync('refreshToken', refreshToken);
              }
              
              resolve({
                success: true,
                token: token,
                user: user,
                isNewUser: isNewUser,
                message: result.message
              });
            } else {
              reject(new Error(result.message || '登录失败'));
            }
          })
          .catch(error => {
            console.error('后端登录失败:', error);
            reject(error);
          });
          
        } else {
          console.error('获取登录凭证失败:', loginRes.errMsg);
          reject(new Error('获取登录凭证失败'));
        }
      },
      fail: (error) => {
        console.error('wx.login调用失败:', error);
        reject(new Error('微信登录失败'));
      }
    });
  });
}

/**
 * 刷新Token
 * @returns {Promise} 刷新结果
 */
function refreshToken() {
  return new Promise((resolve, reject) => {
    try {
      const refreshToken = wx.getStorageSync('refreshToken');
      
      if (!refreshToken) {
        reject(new Error('没有刷新令牌'));
        return;
      }
      
      api.post('/api/auth/refresh', {
        refreshToken: refreshToken
      })
      .then(result => {
        if (result.code === 200) {
          const { token, user } = result.data;
          
          // 更新登录状态
          const app = getApp();
          app.setLoginStatus(token, user);
          
          resolve({
            success: true,
            token: token,
            user: user
          });
        } else {
          reject(new Error(result.message || 'Token刷新失败'));
        }
      })
      .catch(error => {
        console.error('Token刷新失败:', error);
        reject(error);
      });
      
    } catch (error) {
      console.error('获取刷新令牌失败:', error);
      reject(new Error('获取刷新令牌失败'));
    }
  });
}

/**
 * 检查Token有效性
 * @returns {Promise} 检查结果
 */
function checkToken() {
  return api.get('/api/auth/check');
}

/**
 * 获取当前用户信息
 * @returns {Promise} 用户信息
 */
function getCurrentUser() {
  return api.get('/api/auth/me');
}

/**
 * 用户登出
 * @returns {Promise} 登出结果
 */
function logout() {
  return new Promise((resolve, reject) => {
    // 调用后端登出接口
    api.post('/api/auth/logout')
      .then(result => {
        console.log('后端登出成功:', result);
        
        // 清除本地登录状态
        const app = getApp();
        app.clearLoginStatus();
        
        // 清除刷新token
        wx.removeStorageSync('refreshToken');
        
        resolve({
          success: true,
          message: result.message || '登出成功'
        });
      })
      .catch(error => {
        console.error('后端登出失败:', error);
        
        // 即使后端登出失败，也清除本地状态
        const app = getApp();
        app.clearLoginStatus();
        wx.removeStorageSync('refreshToken');
        
        resolve({
          success: true,
          message: '登出成功'
        });
      });
  });
}

/**
 * 检查登录状态
 * @returns {Boolean} 是否已登录
 */
function isLoggedIn() {
  const app = getApp();
  return app.isLoggedIn();
}

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息
 */
function getUserInfo() {
  const app = getApp();
  return app.getUserInfo();
}

/**
 * 获取Token
 * @returns {String|null} Token
 */
function getToken() {
  const app = getApp();
  return app.getToken();
}

/**
 * 要求登录（如果未登录则跳转到登录页）
 * @param {String} redirectUrl 登录成功后的跳转地址
 * @returns {Boolean} 是否已登录
 */
function requireLogin(redirectUrl = '') {
  if (isLoggedIn()) {
    return true;
  }
  
  // 保存当前页面路径，登录成功后跳转回来
  if (redirectUrl) {
    wx.setStorageSync('loginRedirectUrl', redirectUrl);
  } else {
    const pages = getCurrentPages();
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1];
      const url = '/' + currentPage.route;
      if (url !== '/pages/login/login') {
        wx.setStorageSync('loginRedirectUrl', url);
      }
    }
  }
  
  // 跳转到登录页
  wx.reLaunch({
    url: '/pages/login/login'
  });
  
  return false;
}

/**
 * 处理登录成功后的跳转
 */
function handleLoginSuccess() {
  try {
    const redirectUrl = wx.getStorageSync('loginRedirectUrl');
    wx.removeStorageSync('loginRedirectUrl');
    
    if (redirectUrl && redirectUrl !== '/pages/login/login') {
      wx.reLaunch({
        url: redirectUrl
      });
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  } catch (error) {
    console.error('处理登录跳转失败:', error);
    wx.switchTab({
      url: '/pages/index/index'
    });
  }
}

/**
 * 自动登录（尝试使用本地token或刷新token）
 * @returns {Promise} 自动登录结果
 */
function autoLogin() {
  return new Promise((resolve, reject) => {
    if (isLoggedIn()) {
      // 已经登录，检查token有效性
      checkToken()
        .then(result => {
          if (result.code === 200) {
            resolve({ success: true, message: '已登录' });
          } else {
            // Token无效，尝试刷新
            return refreshToken();
          }
        })
        .then(result => {
          if (result && result.success) {
            resolve({ success: true, message: 'Token刷新成功' });
          } else {
            reject(new Error('Token验证失败'));
          }
        })
        .catch(error => {
          console.error('自动登录失败:', error);
          reject(error);
        });
    } else {
      // 未登录，尝试刷新token
      refreshToken()
        .then(result => {
          if (result.success) {
            resolve({ success: true, message: '自动登录成功' });
          } else {
            reject(new Error('自动登录失败'));
          }
        })
        .catch(error => {
          console.error('自动登录失败:', error);
          reject(error);
        });
    }
  });
}

module.exports = {
  wechatLogin,
  refreshToken,
  checkToken,
  getCurrentUser,
  logout,
  isLoggedIn,
  getUserInfo,
  getToken,
  requireLogin,
  handleLoginSuccess,
  autoLogin
};
