<!-- 个人中心页面 -->
<view class="profile-container">
  <!-- 用户头像和基本信息 -->
  <view class="profile-header">
    <view class="avatar-section">
      <image 
        class="user-avatar" 
        src="{{userInfo.avatar_url || '/images/default-avatar.png'}}" 
        mode="aspectFill"
        bindtap="changeAvatar"
      ></image>
      <view class="avatar-edit-btn" bindtap="changeAvatar">
        <image class="edit-icon" src="/images/camera.png"></image>
      </view>
    </view>
    
    <view class="user-basic-info">
      <text class="user-nickname">{{userInfo.nickname || '微信用户'}}</text>
      <text class="user-id">ID: {{userInfo.id}}</text>
    </view>
  </view>

  <!-- 个人信息编辑 -->
  <view class="info-section">
    <view class="section-title">个人信息</view>
    
    <view class="info-item" bindtap="editNickname">
      <text class="info-label">昵称</text>
      <view class="info-value-container">
        <text class="info-value">{{userInfo.nickname || '未设置'}}</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
    
    <view class="info-item">
      <text class="info-label">性别</text>
      <view class="info-value-container">
        <text class="info-value">{{genderText}}</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
    
    <view class="info-item">
      <text class="info-label">地区</text>
      <view class="info-value-container">
        <text class="info-value">{{locationText}}</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
    
    <view class="info-item">
      <text class="info-label">手机号</text>
      <view class="info-value-container">
        <text class="info-value">{{userInfo.phone || '未绑定'}}</text>
        <image class="arrow-icon" src="/images/arrow-right.png"></image>
      </view>
    </view>
  </view>

  <!-- 账户信息 -->
  <view class="account-section">
    <view class="section-title">账户信息</view>
    
    <view class="info-item">
      <text class="info-label">注册时间</text>
      <text class="info-value">{{registerTime}}</text>
    </view>
    
    <view class="info-item">
      <text class="info-label">最后登录</text>
      <text class="info-value">{{lastLoginTime}}</text>
    </view>
    
    <view class="info-item">
      <text class="info-label">登录IP</text>
      <text class="info-value">{{userInfo.last_login_ip || '未知'}}</text>
    </view>
    
    <view class="info-item">
      <text class="info-label">账户状态</text>
      <text class="info-value status-{{userInfo.status === 1 ? 'active' : 'inactive'}}">
        {{userInfo.status === 1 ? '正常' : '已禁用'}}
      </text>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="section-title">功能菜单</view>
    
    <view class="menu-item" bindtap="showTokenInfo">
      <image class="menu-icon" src="/images/token.png"></image>
      <text class="menu-text">Token信息</text>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
    
    <view class="menu-item" bindtap="refreshUserInfo">
      <image class="menu-icon" src="/images/refresh.png"></image>
      <text class="menu-text">刷新信息</text>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
    
    <view class="menu-item" bindtap="exportUserData">
      <image class="menu-icon" src="/images/export.png"></image>
      <text class="menu-text">导出数据</text>
      <image class="arrow-icon" src="/images/arrow-right.png"></image>
    </view>
  </view>

  <!-- 危险操作 -->
  <view class="danger-section">
    <view class="section-title">危险操作</view>
    
    <button class="danger-btn" bindtap="logout">
      退出登录
    </button>
    
    <button class="danger-btn secondary" bindtap="deleteAccount">
      注销账户
    </button>
  </view>
</view>

<!-- 昵称编辑弹窗 -->
<view class="modal-overlay" wx:if="{{showNicknameModal}}" bindtap="closeNicknameModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="modal-header">
      <text class="modal-title">修改昵称</text>
      <image class="close-btn" src="/images/close.png" bindtap="closeNicknameModal"></image>
    </view>
    
    <view class="modal-body">
      <input 
        class="nickname-input" 
        placeholder="请输入新昵称" 
        value="{{newNickname}}"
        bindinput="onNicknameInput"
        maxlength="20"
      />
      <text class="input-tip">昵称长度为1-20个字符</text>
    </view>
    
    <view class="modal-footer">
      <button class="modal-btn cancel" bindtap="closeNicknameModal">取消</button>
      <button class="modal-btn confirm" bindtap="saveNickname">保存</button>
    </view>
  </view>
</view>
