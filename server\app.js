// 服务器入口文件
const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');

// 导入配置和工具
const config = require('./config/config');
const { testConnection } = require('./config/database');
const logger = require('./utils/logger');
const { requestId, requestSizeLimit, sensitiveDataFilter } = require('./middleware/security');

// 导入路由
const authRoutes = require('./routes/auth');

// 创建Express应用
const app = express();

// 信任代理（如果使用反向代理）
app.set('trust proxy', 1);

// 请求ID生成
app.use(requestId);

// 请求大小限制
app.use(requestSizeLimit(10 * 1024 * 1024)); // 10MB

// 安全中间件
app.use(helmet({
  contentSecurityPolicy: false, // 根据需要调整
  crossOriginEmbedderPolicy: false
}));

// CORS配置
app.use(cors(config.security.cors));

// 全局请求频率限制
const globalLimiter = rateLimit({
  windowMs: config.security.rateLimit.windowMs,
  max: config.security.rateLimit.max,
  message: {
    code: 429,
    message: config.security.rateLimit.message,
    data: null
  },
  standardHeaders: true,
  legacyHeaders: false
});
app.use(globalLimiter);

// 解析JSON请求体
app.use(express.json({ 
  limit: '10mb',
  verify: (req, res, buf) => {
    try {
      JSON.parse(buf);
    } catch (e) {
      res.status(400).json({
        code: 400,
        message: '无效的JSON格式',
        data: null
      });
      throw new Error('Invalid JSON');
    }
  }
}));

// 解析URL编码的请求体
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 敏感信息过滤
app.use(sensitiveDataFilter);

// 请求日志中间件
app.use(logger.requestLogger);

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    code: 200,
    message: '服务器运行正常',
    data: {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: config.server.env,
      version: '1.0.0'
    }
  });
});

// API路由
app.use('/api/auth', authRoutes);

// 根路径
app.get('/', (req, res) => {
  res.json({
    code: 200,
    message: '微信小程序登录系统API',
    data: {
      version: '1.0.0',
      environment: config.server.env,
      timestamp: new Date().toISOString(),
      endpoints: {
        health: '/health',
        auth: '/api/auth',
        docs: '/api/docs'
      }
    }
  });
});

// API文档（简单版本）
app.get('/api/docs', (req, res) => {
  res.json({
    code: 200,
    message: 'API文档',
    data: {
      version: '1.0.0',
      baseUrl: `http://${config.server.host}:${config.server.port}`,
      endpoints: [
        {
          method: 'POST',
          path: '/api/auth/login',
          description: '微信小程序登录',
          body: { code: 'string (required)' },
          response: { token: 'string', user: 'object' }
        },
        {
          method: 'POST',
          path: '/api/auth/refresh',
          description: '刷新访问令牌',
          body: { refreshToken: 'string (required)' },
          response: { token: 'string', user: 'object' }
        },
        {
          method: 'GET',
          path: '/api/auth/me',
          description: '获取当前用户信息',
          headers: { Authorization: 'Bearer <token>' },
          response: { user: 'object' }
        },
        {
          method: 'POST',
          path: '/api/auth/logout',
          description: '用户登出',
          headers: { Authorization: 'Bearer <token>' },
          response: { message: 'string' }
        },
        {
          method: 'GET',
          path: '/api/auth/check',
          description: '检查Token有效性',
          headers: { Authorization: 'Bearer <token>' },
          response: { valid: 'boolean', user: 'object' }
        }
      ]
    }
  });
});

// 404处理
app.use('*', (req, res) => {
  res.status(404).json({
    code: 404,
    message: '接口不存在',
    data: null
  });
});

// 错误日志中间件
app.use(logger.errorLogger);

// 全局错误处理中间件
app.use((error, req, res, next) => {
  logger.error('服务器错误', {
    requestId: req.requestId,
    error: error.message,
    stack: error.stack,
    url: req.originalUrl,
    method: req.method,
    ip: req.ip
  });

  // 防止敏感信息泄露
  const isDevelopment = config.server.env === 'development';

  res.status(500).json({
    code: 500,
    message: '服务器内部错误',
    data: isDevelopment ? {
      error: error.message,
      requestId: req.requestId
    } : {
      requestId: req.requestId
    }
  });
});

// 启动服务器
async function startServer() {
  try {
    // 测试数据库连接
    logger.info('正在测试数据库连接...');
    const dbConnected = await testConnection();

    if (!dbConnected) {
      logger.error('数据库连接失败，服务器启动中止');
      process.exit(1);
    }

    // 启动HTTP服务器
    const server = app.listen(config.server.port, config.server.host, () => {
      logger.info('服务器启动成功', {
        host: config.server.host,
        port: config.server.port,
        env: config.server.env,
        pid: process.pid
      });

      console.log('🚀 服务器启动成功!');
      console.log(`📍 地址: http://${config.server.host}:${config.server.port}`);
      console.log(`🌍 环境: ${config.server.env}`);
      console.log(`📚 API文档: http://${config.server.host}:${config.server.port}/api/docs`);
      console.log('按 Ctrl+C 停止服务器');
    });

    // 优雅关闭处理
    process.on('SIGINT', () => {
      logger.info('收到SIGINT信号，正在关闭服务器...');
      console.log('\n正在关闭服务器...');
      server.close(() => {
        logger.info('服务器已关闭');
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });

    process.on('SIGTERM', () => {
      logger.info('收到SIGTERM信号，正在关闭服务器...');
      console.log('\n正在关闭服务器...');
      server.close(() => {
        logger.info('服务器已关闭');
        console.log('✅ 服务器已关闭');
        process.exit(0);
      });
    });

  } catch (error) {
    logger.error('服务器启动失败', { error: error.message, stack: error.stack });
    console.error('❌ 服务器启动失败:', error);
    process.exit(1);
  }
}

// 启动服务器
startServer();

module.exports = app;
