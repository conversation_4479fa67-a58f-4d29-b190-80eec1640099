// 服务器配置文件
require('dotenv').config();

module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    host: process.env.HOST || 'localhost',
    env: process.env.NODE_ENV || 'development'
  },

  // 微信小程序配置
  wechat: {
    appId: process.env.WECHAT_APP_ID || '你的小程序AppID',
    appSecret: process.env.WECHAT_APP_SECRET || '你的小程序AppSecret',
    // 微信API地址
    apiUrl: 'https://api.weixin.qq.com/sns/jscode2session'
  },

  // 数据库配置
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '123456',
    database: process.env.DB_NAME || 'wechat_miniprogram',
    charset: 'utf8mb4',
    timezone: '+08:00',
    // 连接池配置
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000
  },

  // JWT配置
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    issuer: 'wechat-miniprogram-server',
    audience: 'wechat-miniprogram-client'
  },

  // 安全配置
  security: {
    // 密码加密轮数
    bcryptRounds: 12,
    // 请求频率限制
    rateLimit: {
      windowMs: 15 * 60 * 1000, // 15分钟
      max: 100, // 最多100个请求
      message: '请求过于频繁，请稍后再试'
    },
    // CORS配置
    cors: {
      origin: process.env.CORS_ORIGIN || '*',
      credentials: true,
      optionsSuccessStatus: 200
    }
  },

  // 日志配置
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    enableConsole: true,
    enableFile: false,
    logDir: './logs'
  }
};
