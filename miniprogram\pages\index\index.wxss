/* 首页样式 */

.index-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 40rpx;
}

/* 用户信息卡片 */
.user-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.user-avatar {
  margin-right: 30rpx;
}

.avatar-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-info {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #fff;
  margin-bottom: 10rpx;
}

.user-desc {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
}

.user-status {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-right: 10rpx;
}

.status-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #2ed573;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 未登录提示 */
.login-prompt {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 80rpx 40rpx;
  text-align: center;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.prompt-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
}

.prompt-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.prompt-desc {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 60rpx;
  line-height: 1.5;
}

.prompt-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(135deg, #1aad19 0%, #2dc653 100%);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
}

/* 功能菜单 */
.menu-section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-left: 20rpx;
}

.menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.menu-item {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx 30rpx;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.menu-item:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.menu-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

/* 系统信息 */
.system-info {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.info-list {
  margin-top: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f5f5f5;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 28rpx;
  color: #666;
}

.info-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.status-online {
  color: #2ed573;
}

.status-offline {
  color: #ff4757;
}

/* 快速操作 */
.quick-actions {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.action-list {
  margin-top: 20rpx;
}

.action-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  margin-bottom: 20rpx;
}

.action-btn:last-child {
  margin-bottom: 0;
}

.action-btn.primary {
  background: linear-gradient(135deg, #1aad19 0%, #2dc653 100%);
  color: #fff;
}

.action-btn.secondary {
  background-color: #f8f8f8;
  color: #666;
  border: 2rpx solid #e5e5e5;
}

.action-btn.danger {
  background-color: #ff4757;
  color: #fff;
}

/* 底部信息 */
.footer-info {
  text-align: center;
  padding: 40rpx 0;
}

.footer-text {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.6;
}
