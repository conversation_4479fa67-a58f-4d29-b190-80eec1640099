<!-- 登录页面 -->
<view class="login-container">
  <!-- 顶部logo区域 -->
  <view class="logo-section">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="app-name">微信登录系统</text>
    <text class="app-desc">安全便捷的小程序登录体验</text>
  </view>

  <!-- 登录卡片 -->
  <view class="login-card">
    <view class="card-header">
      <text class="card-title">欢迎使用</text>
      <text class="card-subtitle">请使用微信账号登录</text>
    </view>

    <view class="card-body">
      <!-- 登录按钮 -->
      <button 
        class="login-btn {{isLoading ? 'btn-disabled' : ''}}" 
        bindtap="handleLogin"
        disabled="{{isLoading}}"
      >
        <view class="btn-content">
          <image wx:if="{{!isLoading}}" class="wechat-icon" src="/images/wechat.png"></image>
          <text class="btn-text">{{isLoading ? '登录中...' : '微信一键登录'}}</text>
        </view>
      </button>

      <!-- 登录说明 -->
      <view class="login-tips">
        <text class="tip-text">登录即表示同意</text>
        <text class="tip-link" bindtap="showUserAgreement">《用户协议》</text>
        <text class="tip-text">和</text>
        <text class="tip-link" bindtap="showPrivacyPolicy">《隐私政策》</text>
      </view>
    </view>
  </view>

  <!-- 功能介绍 -->
  <view class="feature-section">
    <view class="feature-title">功能特色</view>
    <view class="feature-list">
      <view class="feature-item">
        <image class="feature-icon" src="/images/secure.png"></image>
        <view class="feature-content">
          <text class="feature-name">安全可靠</text>
          <text class="feature-desc">采用微信官方登录，保障账号安全</text>
        </view>
      </view>
      <view class="feature-item">
        <image class="feature-icon" src="/images/fast.png"></image>
        <view class="feature-content">
          <text class="feature-name">快速便捷</text>
          <text class="feature-desc">一键登录，无需注册，即刻体验</text>
        </view>
      </view>
      <view class="feature-item">
        <image class="feature-icon" src="/images/privacy.png"></image>
        <view class="feature-content">
          <text class="feature-name">隐私保护</text>
          <text class="feature-desc">严格保护用户隐私，不泄露个人信息</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部信息 -->
  <view class="footer">
    <text class="footer-text">© 2024 微信登录系统</text>
    <text class="footer-text">技术支持：小程序开发团队</text>
  </view>
</view>

<!-- 加载遮罩 -->
<view class="loading-mask" wx:if="{{isLoading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在登录...</text>
  </view>
</view>
