// JWT工具函数
const jwt = require('jsonwebtoken');
const config = require('../config/config');

/**
 * 生成JWT Token
 * @param {Object} payload - 要编码的数据
 * @param {String} expiresIn - 过期时间（可选）
 * @returns {String} JWT Token
 */
function generateToken(payload, expiresIn = null) {
  try {
    const options = {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
      expiresIn: expiresIn || config.jwt.expiresIn
    };

    return jwt.sign(payload, config.jwt.secret, options);
  } catch (error) {
    console.error('生成JWT Token失败:', error);
    throw new Error('Token生成失败');
  }
}

/**
 * 验证JWT Token
 * @param {String} token - 要验证的Token
 * @returns {Object} 解码后的数据
 */
function verifyToken(token) {
  try {
    const options = {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience
    };

    return jwt.verify(token, config.jwt.secret, options);
  } catch (error) {
    if (error.name === 'TokenExpiredError') {
      throw new Error('Token已过期');
    } else if (error.name === 'JsonWebTokenError') {
      throw new Error('Token无效');
    } else if (error.name === 'NotBeforeError') {
      throw new Error('Token尚未生效');
    } else {
      console.error('验证JWT Token失败:', error);
      throw new Error('Token验证失败');
    }
  }
}

/**
 * 解码JWT Token（不验证签名）
 * @param {String} token - 要解码的Token
 * @returns {Object} 解码后的数据
 */
function decodeToken(token) {
  try {
    return jwt.decode(token, { complete: true });
  } catch (error) {
    console.error('解码JWT Token失败:', error);
    throw new Error('Token解码失败');
  }
}

/**
 * 检查Token是否即将过期
 * @param {String} token - 要检查的Token
 * @param {Number} threshold - 阈值（秒），默认1小时
 * @returns {Boolean} 是否即将过期
 */
function isTokenExpiringSoon(token, threshold = 3600) {
  try {
    const decoded = decodeToken(token);
    const now = Math.floor(Date.now() / 1000);
    const exp = decoded.payload.exp;
    
    return (exp - now) <= threshold;
  } catch (error) {
    return true; // 如果无法解码，认为需要刷新
  }
}

/**
 * 生成刷新Token
 * @param {Object} payload - 要编码的数据
 * @returns {String} 刷新Token
 */
function generateRefreshToken(payload) {
  try {
    const options = {
      issuer: config.jwt.issuer,
      audience: config.jwt.audience,
      expiresIn: '30d' // 刷新Token有效期30天
    };

    return jwt.sign(payload, config.jwt.secret, options);
  } catch (error) {
    console.error('生成刷新Token失败:', error);
    throw new Error('刷新Token生成失败');
  }
}

/**
 * 从请求头中提取Token
 * @param {Object} req - Express请求对象
 * @returns {String|null} Token或null
 */
function extractTokenFromRequest(req) {
  // 从Authorization头中提取
  const authHeader = req.headers.authorization;
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // 从查询参数中提取
  if (req.query.token) {
    return req.query.token;
  }

  // 从请求体中提取
  if (req.body.token) {
    return req.body.token;
  }

  return null;
}

module.exports = {
  generateToken,
  verifyToken,
  decodeToken,
  isTokenExpiringSoon,
  generateRefreshToken,
  extractTokenFromRequest
};
